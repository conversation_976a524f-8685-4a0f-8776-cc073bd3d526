#!/usr/bin/env python3
"""
Test RunPod CLI functionality on Windows.
This script tests the runpodctl.exe that you already have.
"""

import subprocess
import json
import os

def test_runpod_cli():
    """Test the RunPod CLI tool."""
    print("=" * 60)
    print("Testing RunPod CLI (runpodctl.exe)")
    print("=" * 60)
    
    try:
        # Test if runpodctl.exe exists and is executable
        result = subprocess.run(['runpodctl.exe', '--version'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ RunPod CLI is working!")
            print(f"Version: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ RunPod CLI error: {result.stderr}")
            return False
            
    except FileNotFoundError:
        print("❌ runpodctl.exe not found in current directory")
        return False
    except subprocess.TimeoutExpired:
        print("❌ RunPod CLI command timed out")
        return False
    except Exception as e:
        print(f"❌ Error testing RunPod CLI: {e}")
        return False

def configure_runpod_cli():
    """Configure RunPod CLI with API key."""
    print("\n" + "=" * 60)
    print("Configuring RunPod CLI")
    print("=" * 60)
    
    api_key = "rpa_RB6ABSXXW9HPJAB8ZB3YOKT6IP53X7EG6F2VVN3M1slbla"
    
    try:
        # Configure the CLI with your API key
        result = subprocess.run(['runpodctl.exe', 'config', api_key], 
                              capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ RunPod CLI configured successfully!")
            print("✅ API key has been set")
            return True
        else:
            print(f"❌ Configuration failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error configuring RunPod CLI: {e}")
        return False

def test_runpod_operations():
    """Test basic RunPod operations."""
    print("\n" + "=" * 60)
    print("Testing RunPod Operations")
    print("=" * 60)
    
    operations = [
        (['runpodctl.exe', 'get', 'pod'], "List pods"),
        (['runpodctl.exe', 'get', 'gpu'], "List GPU types"),
    ]
    
    success_count = 0
    
    for cmd, description in operations:
        try:
            print(f"\n🔍 Testing: {description}")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print(f"✅ {description} - Success")
                # Print first few lines of output
                lines = result.stdout.strip().split('\n')[:3]
                for line in lines:
                    if line.strip():
                        print(f"   {line}")
                if len(result.stdout.strip().split('\n')) > 3:
                    print("   ...")
                success_count += 1
            else:
                print(f"❌ {description} - Failed: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            print(f"⏰ {description} - Timed out")
        except Exception as e:
            print(f"❌ {description} - Error: {e}")
    
    return success_count

def show_cli_usage_examples():
    """Show examples of how to use RunPod CLI."""
    print("\n" + "=" * 60)
    print("RunPod CLI Usage Examples")
    print("=" * 60)
    
    examples = [
        ("List all pods", "runpodctl.exe get pod"),
        ("List GPU types", "runpodctl.exe get gpu"),
        ("Create a pod", "runpodctl.exe create pod --name my-pod --image runpod/pytorch --gpu-type 'NVIDIA RTX 3090'"),
        ("Stop a pod", "runpodctl.exe stop pod POD_ID"),
        ("Delete a pod", "runpodctl.exe delete pod POD_ID"),
        ("Get pod logs", "runpodctl.exe logs POD_ID"),
        ("SSH into pod", "runpodctl.exe ssh POD_ID"),
    ]
    
    print("Here are common RunPod CLI commands you can use:")
    print()
    
    for description, command in examples:
        print(f"📝 {description}:")
        print(f"   {command}")
        print()

def main():
    """Main test function."""
    print("RunPod CLI Test for Windows")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 3
    
    # Test 1: CLI availability
    if test_runpod_cli():
        tests_passed += 1
    
    # Test 2: CLI configuration
    if configure_runpod_cli():
        tests_passed += 1
    
    # Test 3: Basic operations
    operations_success = test_runpod_operations()
    if operations_success > 0:
        tests_passed += 1
    
    # Show usage examples
    show_cli_usage_examples()
    
    # Summary
    print("=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"Tests passed: {tests_passed}/{total_tests}")
    
    if tests_passed >= 2:
        print("🎉 RunPod CLI is working! You can use it for RunPod operations.")
        print("\n✨ RECOMMENDATION:")
        print("   Use the RunPod CLI (runpodctl.exe) for all RunPod operations on Windows.")
        print("   The Python SDK has Windows compatibility issues, but the CLI works great!")
    else:
        print("⚠️  Some tests failed. Check the errors above.")
    
    return tests_passed >= 2

if __name__ == "__main__":
    main()
