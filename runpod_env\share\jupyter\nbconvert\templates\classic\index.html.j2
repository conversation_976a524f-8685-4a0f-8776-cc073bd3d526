{%- extends 'base.html.j2' -%}
{% from 'mathjax.html.j2' import mathjax %}
{% from 'jupyter_widgets.html.j2' import jupyter_widgets %}

{%- block header -%}
<!DOCTYPE html>
<html lang="{{ resources.language_code }}">
<head>
{%- block html_head -%}
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0">
{% set nb_title = nb.metadata.get('title', resources['metadata']['name']) | escape_html_keep_quotes %}
<title>{{nb_title}}</title>

{%- block html_head_js -%}
{%- block html_head_js_jquery -%}
<script src="{{ resources.jquery_url }}"></script>
{%- endblock html_head_js_jquery -%}
{%- block html_head_js_requirejs -%}
<script src="{{ resources.require_js_url }}"></script>
{%- endblock html_head_js_requirejs -%}
{%- block html_head_js_mermaidjs -%}
<script type="module">
  import mermaid from '{{ resources.mermaid_js_url }}';
  mermaid.initialize({ startOnLoad: true });
</script>
{%- endblock html_head_js_mermaidjs -%}
{%- endblock html_head_js -%}

{% block jupyter_widgets %}
  {%- if "widgets" in nb.metadata -%}
    {{ jupyter_widgets(resources.jupyter_widgets_base_url, resources.html_manager_semver_range, resources.widget_renderer_url) }}
  {%- endif -%}
{% endblock jupyter_widgets %}

{% for css in resources.inlining.css -%}
  <style type="text/css">
  {{ css }}
  </style>
{% endfor %}

{% block notebook_css %}
{{ resources.include_css("static/style.css") }}
<style type="text/css">
/* Overrides of notebook CSS for static HTML export */
body {
  overflow: visible;
  padding: 8px;
}

div#notebook {
  overflow: visible;
  border-top: none;
}

{%- if resources.global_content_filter.no_prompt-%}
div#notebook-container{
  padding: 6ex 12ex 8ex 12ex;
}
{%- endif -%}

@media print {
  body {
    margin: 0;
  }
  div.cell {
    display: block;
    page-break-inside: avoid;
  }
  div.output_wrapper {
    display: block;
    page-break-inside: avoid;
  }
  div.output {
    display: block;
    page-break-inside: avoid;
  }
}
</style>
{% endblock notebook_css %}

{%- block html_head_js_mathjax -%}
{{ mathjax(resources.mathjax_url) }}
{%- endblock html_head_js_mathjax -%}

{%- block html_head_css -%}
{%- endblock html_head_css -%}

{%- endblock html_head -%}
</head>
{%- endblock header -%}

{% block body_header %}
<body>
  <main>
    <div tabindex="-1" id="notebook" class="border-box-sizing">
      <div class="container" id="notebook-container">
{% endblock body_header %}

{% block body_footer %}
      </div>
    </div>
  </main>
</body>
{% endblock body_footer %}

{% block footer %}
{% block footer_js %}
{% endblock footer_js %}
{{ super() }}
</html>
{% endblock footer %}
