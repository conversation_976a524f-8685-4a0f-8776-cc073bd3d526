#!/usr/bin/env python3
"""
Direct RunPod API calls using requests library.
This works on Windows without the fcntl dependency issues.
"""

import requests
import json

class RunPodAPI:
    """Direct RunPod API client that works on Windows."""
    
    def __init__(self, api_key):
        self.api_key = api_key
        self.base_url = "https://api.runpod.io/graphql"
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
    
    def _make_request(self, query, variables=None):
        """Make a GraphQL request to RunPod API."""
        payload = {
            "query": query,
            "variables": variables or {}
        }
        
        try:
            response = requests.post(
                self.base_url,
                headers=self.headers,
                json=payload,
                timeout=30
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"API request failed: {e}")
            return None
    
    def get_pods(self):
        """Get all pods."""
        query = """
        query {
            myself {
                pods {
                    id
                    name
                    imageName
                    machineId
                    runtime {
                        uptimeInSeconds
                    }
                    machine {
                        podHostId
                    }
                }
            }
        }
        """
        return self._make_request(query)
    
    def get_gpu_types(self):
        """Get available GPU types."""
        query = """
        query {
            gpuTypes {
                id
                displayName
                memoryInGb
                secureCloud
                communityCloud
                lowestPrice {
                    minimumBidPrice
                    uninterruptablePrice
                }
            }
        }
        """
        return self._make_request(query)
    
    def test_connection(self):
        """Test API connection."""
        query = """
        query {
            myself {
                id
                email
            }
        }
        """
        return self._make_request(query)

def main():
    """Test the direct API approach."""
    print("RunPod Direct API Test (Windows Compatible)")
    print("=" * 60)
    
    # Your API key
    api_key = "rpa_RB6ABSXXW9HPJAB8ZB3YOKT6IP53X7EG6F2VVN3M1slbla"
    
    # Create API client
    client = RunPodAPI(api_key)
    
    # Test connection
    print("🔍 Testing API connection...")
    result = client.test_connection()
    
    if result and 'data' in result:
        print("✅ API connection successful!")
        if 'myself' in result['data']:
            user_info = result['data']['myself']
            print(f"✅ User ID: {user_info.get('id', 'N/A')}")
            print(f"✅ Email: {user_info.get('email', 'N/A')}")
    else:
        print("❌ API connection failed")
        if result and 'errors' in result:
            for error in result['errors']:
                print(f"   Error: {error.get('message', 'Unknown error')}")
        return False
    
    # Test getting pods
    print("\n🔍 Testing get pods...")
    pods_result = client.get_pods()
    
    if pods_result and 'data' in pods_result:
        pods = pods_result['data']['myself']['pods']
        print(f"✅ Found {len(pods)} pods")
        for pod in pods[:3]:  # Show first 3 pods
            print(f"   Pod: {pod['name']} (ID: {pod['id']})")
    else:
        print("❌ Failed to get pods")
    
    # Test getting GPU types
    print("\n🔍 Testing get GPU types...")
    gpu_result = client.get_gpu_types()
    
    if gpu_result and 'data' in gpu_result:
        gpus = gpu_result['data']['gpuTypes']
        print(f"✅ Found {len(gpus)} GPU types")
        for gpu in gpus[:3]:  # Show first 3 GPU types
            print(f"   GPU: {gpu['displayName']} ({gpu['memoryInGb']}GB)")
    else:
        print("❌ Failed to get GPU types")
    
    print("\n" + "=" * 60)
    print("✨ This approach works on Windows!")
    print("You can extend this class to add more RunPod operations.")
    
    return True

if __name__ == "__main__":
    main()
