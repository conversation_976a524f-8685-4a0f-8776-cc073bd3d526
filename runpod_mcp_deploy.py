#!/usr/bin/env python3
"""
RunPod MCP Server deployment script for Qwen3 fine-tuning.
This uses the RunPod MCP server instead of CLI for better integration.
"""

import json
import time
from pathlib import Path

class RunPodMCPManager:
    """Manage RunPod operations using the MCP server."""
    
    def __init__(self):
        self.pod_id = None
        
    def list_pods(self):
        """List all pods."""
        print("📋 Listing your pods...")
        try:
            # This will call the MCP server function
            result = list_pods_RunPod()
            print("✅ Pods retrieved successfully!")
            return result
        except Exception as e:
            print(f"❌ Failed to list pods: {e}")
            return None
    
    def create_training_pod(self, pod_name="qwen3-training"):
        """Create a pod optimized for training."""
        print(f"🚀 Creating training pod: {pod_name}")
        
        pod_config = {
            "imageName": "runpod/pytorch:2.1.0-py3.10-cuda11.8.0-devel-ubuntu22.04",
            "name": pod_name,
            "gpuCount": 1,
            "volumeInGb": 50,
            "containerDiskInGb": 20,
            "ports": ["8888/http", "22/tcp"],
            "env": {
                "JUPYTER_ENABLE_LAB": "yes",
                "JUPYTER_TOKEN": "runpod"
            }
        }
        
        try:
            result = create_pod_RunPod(**pod_config)
            print("✅ Pod created successfully!")
            
            if isinstance(result, dict) and 'id' in result:
                self.pod_id = result['id']
                print(f"📝 Pod ID: {self.pod_id}")
                return True, result
            else:
                print(f"📄 Result: {result}")
                return True, result
                
        except Exception as e:
            print(f"❌ Failed to create pod: {e}")
            return False, str(e)
    
    def get_pod_info(self, pod_id):
        """Get detailed information about a pod."""
        print(f"🔍 Getting info for pod: {pod_id}")
        
        try:
            result = get_pod_RunPod(podId=pod_id, includeMachine=True)
            print("✅ Pod info retrieved!")
            return result
        except Exception as e:
            print(f"❌ Failed to get pod info: {e}")
            return None
    
    def start_pod(self, pod_id):
        """Start a pod."""
        print(f"▶️ Starting pod: {pod_id}")
        
        try:
            result = start_pod_RunPod(podId=pod_id)
            print("✅ Pod start command sent!")
            return result
        except Exception as e:
            print(f"❌ Failed to start pod: {e}")
            return None
    
    def stop_pod(self, pod_id):
        """Stop a pod."""
        print(f"⏹️ Stopping pod: {pod_id}")
        
        try:
            result = stop_pod_RunPod(podId=pod_id)
            print("✅ Pod stop command sent!")
            return result
        except Exception as e:
            print(f"❌ Failed to stop pod: {e}")
            return None
    
    def delete_pod(self, pod_id):
        """Delete a pod."""
        print(f"🗑️ Deleting pod: {pod_id}")
        
        try:
            result = delete_pod_RunPod(podId=pod_id)
            print("✅ Pod deletion command sent!")
            return result
        except Exception as e:
            print(f"❌ Failed to delete pod: {e}")
            return None
    
    def wait_for_pod_ready(self, pod_id, max_wait_minutes=10):
        """Wait for pod to be in running state."""
        print(f"⏳ Waiting for pod {pod_id} to be ready...")
        
        for attempt in range(max_wait_minutes):
            try:
                pod_info = self.get_pod_info(pod_id)
                
                if pod_info and isinstance(pod_info, dict):
                    # Check pod status (this might need adjustment based on actual response structure)
                    status = pod_info.get('desiredStatus', 'unknown')
                    runtime_status = pod_info.get('runtime', {}).get('status', 'unknown')
                    
                    print(f"   Attempt {attempt + 1}/{max_wait_minutes}: Status = {status}, Runtime = {runtime_status}")
                    
                    if status == 'RUNNING' or runtime_status == 'RUNNING':
                        print("✅ Pod is ready!")
                        return True
                
                if attempt < max_wait_minutes - 1:
                    print(f"   Waiting 60 seconds before next check...")
                    time.sleep(60)
                    
            except Exception as e:
                print(f"   Error checking pod status: {e}")
                time.sleep(60)
        
        print("❌ Pod did not become ready within the timeout period")
        return False
    
    def create_training_template(self):
        """Create a reusable template for training."""
        print("📄 Creating training template...")
        
        template_config = {
            "name": "Qwen3 Training Template",
            "imageName": "runpod/pytorch:2.1.0-py3.10-cuda11.8.0-devel-ubuntu22.04",
            "env": {
                "JUPYTER_ENABLE_LAB": "yes",
                "JUPYTER_TOKEN": "runpod",
                "PYTHONPATH": "/workspace"
            },
            "ports": ["8888/http", "22/tcp"],
            "volumeInGb": 50,
            "containerDiskInGb": 20,
            "readme": """# Qwen3 Training Template

This template is optimized for fine-tuning Qwen3 models.

## Included:
- PyTorch 2.1.0
- CUDA 11.8
- Jupyter Lab
- 50GB volume storage
- 20GB container disk

## Usage:
1. Upload your training script
2. Install requirements: `pip install -r requirements.txt`
3. Run training: `python finetune_qwen3.py`
"""
        }
        
        try:
            result = create_template_RunPod(**template_config)
            print("✅ Training template created!")
            return result
        except Exception as e:
            print(f"❌ Failed to create template: {e}")
            return None

def show_deployment_menu():
    """Show deployment options."""
    print("\n" + "=" * 60)
    print("🚀 RunPod MCP Training Deployment")
    print("=" * 60)
    print("1. List existing pods")
    print("2. Create new training pod")
    print("3. Get pod information")
    print("4. Start pod")
    print("5. Stop pod")
    print("6. Delete pod")
    print("7. Create training template")
    print("8. List templates")
    print("9. Exit")
    print("=" * 60)

def main():
    """Main deployment workflow."""
    manager = RunPodMCPManager()
    
    while True:
        show_deployment_menu()
        choice = input("\nEnter your choice (1-9): ").strip()
        
        if choice == "1":
            result = manager.list_pods()
            if result:
                print(f"📄 Pods: {json.dumps(result, indent=2)}")
        
        elif choice == "2":
            pod_name = input("Pod name (default: qwen3-training): ").strip() or "qwen3-training"
            success, result = manager.create_training_pod(pod_name)
            if success:
                print(f"📄 Pod created: {json.dumps(result, indent=2)}")
                
                # Ask if user wants to wait for pod to be ready
                wait = input("Wait for pod to be ready? (y/N): ").strip().lower()
                if wait == 'y' and manager.pod_id:
                    manager.wait_for_pod_ready(manager.pod_id)
        
        elif choice == "3":
            pod_id = input("Enter pod ID: ").strip()
            if pod_id:
                result = manager.get_pod_info(pod_id)
                if result:
                    print(f"📄 Pod info: {json.dumps(result, indent=2)}")
        
        elif choice == "4":
            pod_id = input("Enter pod ID to start: ").strip()
            if pod_id:
                result = manager.start_pod(pod_id)
                if result:
                    print(f"📄 Start result: {json.dumps(result, indent=2)}")
        
        elif choice == "5":
            pod_id = input("Enter pod ID to stop: ").strip()
            if pod_id:
                result = manager.stop_pod(pod_id)
                if result:
                    print(f"📄 Stop result: {json.dumps(result, indent=2)}")
        
        elif choice == "6":
            pod_id = input("Enter pod ID to delete: ").strip()
            if pod_id:
                confirm = input(f"Are you sure you want to delete pod {pod_id}? (y/N): ").strip().lower()
                if confirm == 'y':
                    result = manager.delete_pod(pod_id)
                    if result:
                        print(f"📄 Delete result: {json.dumps(result, indent=2)}")
        
        elif choice == "7":
            result = manager.create_training_template()
            if result:
                print(f"📄 Template created: {json.dumps(result, indent=2)}")
        
        elif choice == "8":
            try:
                result = list_templates_RunPod()
                print(f"📄 Templates: {json.dumps(result, indent=2)}")
            except Exception as e:
                print(f"❌ Failed to list templates: {e}")
        
        elif choice == "9":
            print("👋 Goodbye!")
            break
        
        else:
            print("❌ Invalid choice")

if __name__ == "__main__":
    main()
