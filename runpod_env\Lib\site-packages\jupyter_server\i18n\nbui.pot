# Translations template for Jupy<PERSON>.
# Copyright (C) 2017 ORGANIZATION
# This file is distributed under the same license as the Jupyter project.
# <AUTHOR> <EMAIL>, 2017.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Jupyter VERSION\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2017-07-07 12:48-0500\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.3.4\n"

#: notebook/templates/404.html:3
msgid "You are requesting a page that does not exist!"
msgstr ""

#: notebook/templates/edit.html:37
msgid "current mode"
msgstr ""

#: notebook/templates/edit.html:48 notebook/templates/notebook.html:78
msgid "File"
msgstr ""

#: notebook/templates/edit.html:50 notebook/templates/tree.html:57
msgid "New"
msgstr ""

#: notebook/templates/edit.html:51
msgid "Save"
msgstr ""

#: notebook/templates/edit.html:52 notebook/templates/tree.html:36
msgid "Rename"
msgstr ""

#: notebook/templates/edit.html:53 notebook/templates/tree.html:38
msgid "Download"
msgstr ""

#: notebook/templates/edit.html:56 notebook/templates/notebook.html:131
#: notebook/templates/tree.html:41
msgid "Edit"
msgstr ""

#: notebook/templates/edit.html:58
msgid "Find"
msgstr ""

#: notebook/templates/edit.html:59
msgid "Find &amp; Replace"
msgstr ""

#: notebook/templates/edit.html:61
msgid "Key Map"
msgstr ""

#: notebook/templates/edit.html:62
msgid "Default"
msgstr ""

#: notebook/templates/edit.html:63
msgid "Sublime Text"
msgstr ""

#: notebook/templates/edit.html:68 notebook/templates/notebook.html:159
#: notebook/templates/tree.html:40
msgid "View"
msgstr ""

#: notebook/templates/edit.html:70 notebook/templates/notebook.html:162
msgid "Show/Hide the logo and notebook title (above menu bar)"
msgstr ""

#: notebook/templates/edit.html:71 notebook/templates/notebook.html:163
msgid "Toggle Header"
msgstr ""

#: notebook/templates/edit.html:72 notebook/templates/notebook.html:171
msgid "Toggle Line Numbers"
msgstr ""

#: notebook/templates/edit.html:75
msgid "Language"
msgstr ""

#: notebook/templates/error.html:23
msgid "The error was:"
msgstr ""

#: notebook/templates/login.html:24
msgid "Password or token:"
msgstr ""

#: notebook/templates/login.html:26
msgid "Password:"
msgstr ""

#: notebook/templates/login.html:31
msgid "Log in"
msgstr ""

#: notebook/templates/login.html:39
msgid "No login available, you shouldn't be seeing this page."
msgstr ""

#: notebook/templates/logout.html:24
#, python-format
msgid "Proceed to the <a href=\"%(base_url)s\">dashboard"
msgstr ""

#: notebook/templates/logout.html:26
#, python-format
msgid "Proceed to the <a href=\"%(base_url)slogin\">login page"
msgstr ""

#: notebook/templates/notebook.html:62
msgid "Menu"
msgstr ""

#: notebook/templates/notebook.html:65 notebook/templates/notebook.html:254
msgid "Kernel"
msgstr ""

#: notebook/templates/notebook.html:68
msgid "This notebook is read-only"
msgstr ""

#: notebook/templates/notebook.html:81
msgid "New Notebook"
msgstr ""

#: notebook/templates/notebook.html:85
msgid "Opens a new window with the Dashboard view"
msgstr ""

#: notebook/templates/notebook.html:86
msgid "Open..."
msgstr ""

#: notebook/templates/notebook.html:90
msgid "Open a copy of this notebook's contents and start a new kernel"
msgstr ""

#: notebook/templates/notebook.html:91
msgid "Make a Copy..."
msgstr ""

#: notebook/templates/notebook.html:92
msgid "Rename..."
msgstr ""

#: notebook/templates/notebook.html:93
msgid "Save and Checkpoint"
msgstr ""

#: notebook/templates/notebook.html:96
msgid "Revert to Checkpoint"
msgstr ""

#: notebook/templates/notebook.html:106
msgid "Print Preview"
msgstr ""

#: notebook/templates/notebook.html:107
msgid "Download as"
msgstr ""

#: notebook/templates/notebook.html:109
msgid "Notebook (.ipynb)"
msgstr ""

#: notebook/templates/notebook.html:110
msgid "Script"
msgstr ""

#: notebook/templates/notebook.html:111
msgid "HTML (.html)"
msgstr ""

#: notebook/templates/notebook.html:112
msgid "Markdown (.md)"
msgstr ""

#: notebook/templates/notebook.html:113
msgid "reST (.rst)"
msgstr ""

#: notebook/templates/notebook.html:114
msgid "LaTeX (.tex)"
msgstr ""

#: notebook/templates/notebook.html:115
msgid "PDF via LaTeX (.pdf)"
msgstr ""

#: notebook/templates/notebook.html:118
msgid "Deploy as"
msgstr ""

#: notebook/templates/notebook.html:123
msgid "Trust the output of this notebook"
msgstr ""

#: notebook/templates/notebook.html:124
msgid "Trust Notebook"
msgstr ""

#: notebook/templates/notebook.html:127
msgid "Shutdown this notebook's kernel, and close this window"
msgstr ""

#: notebook/templates/notebook.html:128
msgid "Close and Halt"
msgstr ""

#: notebook/templates/notebook.html:133
msgid "Cut Cells"
msgstr ""

#: notebook/templates/notebook.html:134
msgid "Copy Cells"
msgstr ""

#: notebook/templates/notebook.html:135
msgid "Paste Cells Above"
msgstr ""

#: notebook/templates/notebook.html:136
msgid "Paste Cells Below"
msgstr ""

#: notebook/templates/notebook.html:137
msgid "Paste Cells &amp; Replace"
msgstr ""

#: notebook/templates/notebook.html:138
msgid "Delete Cells"
msgstr ""

#: notebook/templates/notebook.html:139
msgid "Undo Delete Cells"
msgstr ""

#: notebook/templates/notebook.html:141
msgid "Split Cell"
msgstr ""

#: notebook/templates/notebook.html:142
msgid "Merge Cell Above"
msgstr ""

#: notebook/templates/notebook.html:143
msgid "Merge Cell Below"
msgstr ""

#: notebook/templates/notebook.html:145
msgid "Move Cell Up"
msgstr ""

#: notebook/templates/notebook.html:146
msgid "Move Cell Down"
msgstr ""

#: notebook/templates/notebook.html:148
msgid "Edit Notebook Metadata"
msgstr ""

#: notebook/templates/notebook.html:150
msgid "Find and Replace"
msgstr ""

#: notebook/templates/notebook.html:152
msgid "Cut Cell Attachments"
msgstr ""

#: notebook/templates/notebook.html:153
msgid "Copy Cell Attachments"
msgstr ""

#: notebook/templates/notebook.html:154
msgid "Paste Cell Attachments"
msgstr ""

#: notebook/templates/notebook.html:156
msgid "Insert Image"
msgstr ""

#: notebook/templates/notebook.html:166
msgid "Show/Hide the action icons (below menu bar)"
msgstr ""

#: notebook/templates/notebook.html:167
msgid "Toggle Toolbar"
msgstr ""

#: notebook/templates/notebook.html:170
msgid "Show/Hide line numbers in cells"
msgstr ""

#: notebook/templates/notebook.html:174
msgid "Cell Toolbar"
msgstr ""

#: notebook/templates/notebook.html:179
msgid "Insert"
msgstr ""

#: notebook/templates/notebook.html:182
msgid "Insert an empty Code cell above the currently active cell"
msgstr ""

#: notebook/templates/notebook.html:183
msgid "Insert Cell Above"
msgstr ""

#: notebook/templates/notebook.html:185
msgid "Insert an empty Code cell below the currently active cell"
msgstr ""

#: notebook/templates/notebook.html:186
msgid "Insert Cell Below"
msgstr ""

#: notebook/templates/notebook.html:189
msgid "Cell"
msgstr ""

#: notebook/templates/notebook.html:191
msgid "Run this cell, and move cursor to the next one"
msgstr ""

#: notebook/templates/notebook.html:192
msgid "Run Cells"
msgstr ""

#: notebook/templates/notebook.html:193
msgid "Run this cell, select below"
msgstr ""

#: notebook/templates/notebook.html:194
msgid "Run Cells and Select Below"
msgstr ""

#: notebook/templates/notebook.html:195
msgid "Run this cell, insert below"
msgstr ""

#: notebook/templates/notebook.html:196
msgid "Run Cells and Insert Below"
msgstr ""

#: notebook/templates/notebook.html:197
msgid "Run all cells in the notebook"
msgstr ""

#: notebook/templates/notebook.html:198
msgid "Run All"
msgstr ""

#: notebook/templates/notebook.html:199
msgid "Run all cells above (but not including) this cell"
msgstr ""

#: notebook/templates/notebook.html:200
msgid "Run All Above"
msgstr ""

#: notebook/templates/notebook.html:201
msgid "Run this cell and all cells below it"
msgstr ""

#: notebook/templates/notebook.html:202
msgid "Run All Below"
msgstr ""

#: notebook/templates/notebook.html:205
msgid "All cells in the notebook have a cell type. By default, new cells are created as 'Code' cells"
msgstr ""

#: notebook/templates/notebook.html:206
msgid "Cell Type"
msgstr ""

#: notebook/templates/notebook.html:209
msgid "Contents will be sent to the kernel for execution, and output will display in the footer of cell"
msgstr ""

#: notebook/templates/notebook.html:212
msgid "Contents will be rendered as HTML and serve as explanatory text"
msgstr ""

#: notebook/templates/notebook.html:213 notebook/templates/notebook.html:298
msgid "Markdown"
msgstr ""

#: notebook/templates/notebook.html:215
msgid "Contents will pass through nbconvert unmodified"
msgstr ""

#: notebook/templates/notebook.html:216
msgid "Raw NBConvert"
msgstr ""

#: notebook/templates/notebook.html:220
msgid "Current Outputs"
msgstr ""

#: notebook/templates/notebook.html:223
msgid "Hide/Show the output of the current cell"
msgstr ""

#: notebook/templates/notebook.html:224 notebook/templates/notebook.html:240
msgid "Toggle"
msgstr ""

#: notebook/templates/notebook.html:227
msgid "Scroll the output of the current cell"
msgstr ""

#: notebook/templates/notebook.html:228 notebook/templates/notebook.html:244
msgid "Toggle Scrolling"
msgstr ""

#: notebook/templates/notebook.html:231
msgid "Clear the output of the current cell"
msgstr ""

#: notebook/templates/notebook.html:232 notebook/templates/notebook.html:248
msgid "Clear"
msgstr ""

#: notebook/templates/notebook.html:236
msgid "All Output"
msgstr ""

#: notebook/templates/notebook.html:239
msgid "Hide/Show the output of all cells"
msgstr ""

#: notebook/templates/notebook.html:243
msgid "Scroll the output of all cells"
msgstr ""

#: notebook/templates/notebook.html:247
msgid "Clear the output of all cells"
msgstr ""

#: notebook/templates/notebook.html:257
msgid "Send Keyboard Interrupt (CTRL-C) to the Kernel"
msgstr ""

#: notebook/templates/notebook.html:258
msgid "Interrupt"
msgstr ""

#: notebook/templates/notebook.html:261
msgid "Restart the Kernel"
msgstr ""

#: notebook/templates/notebook.html:262
msgid "Restart"
msgstr ""

#: notebook/templates/notebook.html:265
msgid "Restart the Kernel and clear all output"
msgstr ""

#: notebook/templates/notebook.html:266
msgid "Restart &amp; Clear Output"
msgstr ""

#: notebook/templates/notebook.html:269
msgid "Restart the Kernel and re-run the notebook"
msgstr ""

#: notebook/templates/notebook.html:270
msgid "Restart &amp; Run All"
msgstr ""

#: notebook/templates/notebook.html:273
msgid "Reconnect to the Kernel"
msgstr ""

#: notebook/templates/notebook.html:274
msgid "Reconnect"
msgstr ""

#: notebook/templates/notebook.html:282
msgid "Change kernel"
msgstr ""

#: notebook/templates/notebook.html:287
msgid "Help"
msgstr ""

#: notebook/templates/notebook.html:290
msgid "A quick tour of the notebook user interface"
msgstr ""

#: notebook/templates/notebook.html:290
msgid "User Interface Tour"
msgstr ""

#: notebook/templates/notebook.html:291
msgid "Opens a tooltip with all keyboard shortcuts"
msgstr ""

#: notebook/templates/notebook.html:291
msgid "Keyboard Shortcuts"
msgstr ""

#: notebook/templates/notebook.html:292
msgid "Opens a dialog allowing you to edit Keyboard shortcuts"
msgstr ""

#: notebook/templates/notebook.html:292
msgid "Edit Keyboard Shortcuts"
msgstr ""

#: notebook/templates/notebook.html:297
msgid "Notebook Help"
msgstr ""

#: notebook/templates/notebook.html:303
msgid "Opens in a new window"
msgstr ""

#: notebook/templates/notebook.html:319
msgid "About Jupyter Notebook"
msgstr ""

#: notebook/templates/notebook.html:319
msgid "About"
msgstr ""

#: notebook/templates/page.html:114
msgid "Jupyter Notebook requires JavaScript."
msgstr ""

#: notebook/templates/page.html:115
msgid "Please enable it to proceed. "
msgstr ""

#: notebook/templates/page.html:121
msgid "dashboard"
msgstr ""

#: notebook/templates/page.html:132
msgid "Logout"
msgstr ""

#: notebook/templates/page.html:134
msgid "Login"
msgstr ""

#: notebook/templates/tree.html:23
msgid "Files"
msgstr ""

#: notebook/templates/tree.html:24
msgid "Running"
msgstr ""

#: notebook/templates/tree.html:25
msgid "Clusters"
msgstr ""

#: notebook/templates/tree.html:32
msgid "Select items to perform actions on them."
msgstr ""

#: notebook/templates/tree.html:35
msgid "Duplicate selected"
msgstr ""

#: notebook/templates/tree.html:35
msgid "Duplicate"
msgstr ""

#: notebook/templates/tree.html:36
msgid "Rename selected"
msgstr ""

#: notebook/templates/tree.html:37
msgid "Move selected"
msgstr ""

#: notebook/templates/tree.html:37
msgid "Move"
msgstr ""

#: notebook/templates/tree.html:38
msgid "Download selected"
msgstr ""

#: notebook/templates/tree.html:39
msgid "Shutdown selected notebook(s)"
msgstr ""

#: notebook/templates/notebook.html:278
#: notebook/templates/tree.html:39
msgid "Shutdown"
msgstr ""

#: notebook/templates/tree.html:40
msgid "View selected"
msgstr ""

#: notebook/templates/tree.html:41
msgid "Edit selected"
msgstr ""

#: notebook/templates/tree.html:42
msgid "Delete selected"
msgstr ""

#: notebook/templates/tree.html:50
msgid "Click to browse for a file to upload."
msgstr ""

#: notebook/templates/tree.html:51
msgid "Upload"
msgstr ""

#: notebook/templates/tree.html:65
msgid "Text File"
msgstr ""

#: notebook/templates/tree.html:68
msgid "Folder"
msgstr ""

#: notebook/templates/tree.html:72
msgid "Terminal"
msgstr ""

#: notebook/templates/tree.html:76
msgid "Terminals Unavailable"
msgstr ""

#: notebook/templates/tree.html:82
msgid "Refresh notebook list"
msgstr ""

#: notebook/templates/tree.html:90
msgid "Select All / None"
msgstr ""

#: notebook/templates/tree.html:93
msgid "Select..."
msgstr ""

#: notebook/templates/tree.html:98
msgid "Select All Folders"
msgstr ""

#: notebook/templates/tree.html:98
msgid " Folders"
msgstr ""

#: notebook/templates/tree.html:99
msgid "Select All Notebooks"
msgstr ""

#: notebook/templates/tree.html:99
msgid " All Notebooks"
msgstr ""

#: notebook/templates/tree.html:100
msgid "Select Running Notebooks"
msgstr ""

#: notebook/templates/tree.html:100
msgid " Running"
msgstr ""

#: notebook/templates/tree.html:101
msgid "Select All Files"
msgstr ""

#: notebook/templates/tree.html:101
msgid " Files"
msgstr ""

#: notebook/templates/tree.html:114
msgid "Last Modified"
msgstr ""

#: notebook/templates/tree.html:120
msgid "Name"
msgstr ""

#: notebook/templates/tree.html:130
msgid "Currently running Jupyter processes"
msgstr ""

#: notebook/templates/tree.html:134
msgid "Refresh running list"
msgstr ""

#: notebook/templates/tree.html:150
msgid "There are no terminals running."
msgstr ""

#: notebook/templates/tree.html:152
msgid "Terminals are unavailable."
msgstr ""

#: notebook/templates/tree.html:162
msgid "Notebooks"
msgstr ""

#: notebook/templates/tree.html:169
msgid "There are no notebooks running."
msgstr ""

#: notebook/templates/tree.html:178
msgid "Clusters tab is now provided by IPython parallel."
msgstr ""

#: notebook/templates/tree.html:179
msgid "See '<a href=\"https://github.com/ipython/ipyparallel\">IPython parallel</a>' for installation details."
msgstr ""
