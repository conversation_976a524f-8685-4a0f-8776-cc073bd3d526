#!/usr/bin/env python3
"""
Monitor training progress on RunPod using CLI.
"""

import subprocess
import time
import json
import sys

def run_cli_command(cmd, timeout=30):
    """Run a RunPod CLI command."""
    try:
        result = subprocess.run(
            ["./runpodctl.exe"] + cmd,
            capture_output=True,
            text=True,
            timeout=timeout
        )
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def monitor_pod(pod_id):
    """Monitor training on a specific pod."""
    print(f"👀 Monitoring training on pod: {pod_id}")
    print("=" * 50)
    
    while True:
        print(f"\n🕐 {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("-" * 30)
        
        # Check pod status
        success, output, error = run_cli_command(["get", "pod", pod_id])
        if success:
            print("📊 Pod Status: Running" if "running" in output.lower() else "📊 Pod Status: Not Running")
        else:
            print(f"❌ Cannot get pod status: {error}")
            break
        
        # Check GPU usage
        success, output, error = run_cli_command(["exec", pod_id, "--", "nvidia-smi", "--query-gpu=utilization.gpu,memory.used,memory.total,temperature.gpu", "--format=csv,noheader,nounits"])
        if success and output.strip():
            lines = output.strip().split('\n')
            for i, line in enumerate(lines):
                if line.strip():
                    parts = line.split(',')
                    if len(parts) >= 4:
                        gpu_util, mem_used, mem_total, temp = [p.strip() for p in parts[:4]]
                        print(f"🎮 GPU {i}: {gpu_util}% util, {mem_used}/{mem_total}MB memory, {temp}°C")
        else:
            print("⚠️  Cannot get GPU info")
        
        # Check if training files exist
        success, output, error = run_cli_command(["exec", pod_id, "--", "ls", "-la", "/workspace/"])
        if success:
            if "qwen3-python-finetune" in output:
                print("📁 Training directory exists")
                
                # Check training progress
                success, output, error = run_cli_command(["exec", pod_id, "--", "find", "/workspace/qwen3-python-finetune", "-name", "*.json", "-type", "f"])
                if success and output.strip():
                    print("📈 Training files found")
                else:
                    print("⏳ Training files not yet created")
            else:
                print("⚠️  Training directory not found")
        
        # Check for completed model
        success, output, error = run_cli_command(["exec", pod_id, "--", "ls", "-la", "/workspace/qwen3-python-final/"])
        if success and "model_info.json" in output:
            print("🎉 Training appears to be complete!")
            print("📥 You can now download the results:")
            print(f"   ./runpodctl.exe receive {pod_id} /workspace/qwen3-python-final ./results/")
            break
        
        # Wait before next check
        print("\n⏳ Waiting 60 seconds before next check...")
        print("   Press Ctrl+C to stop monitoring")
        try:
            time.sleep(60)
        except KeyboardInterrupt:
            print("\n👋 Monitoring stopped by user")
            break

def main():
    """Main monitoring function."""
    print("👀 RunPod Training Monitor")
    print("=" * 30)
    
    # Get pod ID
    if len(sys.argv) > 1:
        pod_id = sys.argv[1]
    else:
        # List pods first
        print("📋 Your pods:")
        success, output, error = run_cli_command(["get", "pod"])
        if success:
            print(output)
        else:
            print(f"❌ Cannot list pods: {error}")
            return
        
        pod_id = input("\nEnter pod ID to monitor: ").strip()
    
    if not pod_id:
        print("❌ Pod ID required")
        return
    
    # Start monitoring
    monitor_pod(pod_id)

if __name__ == "__main__":
    main()
