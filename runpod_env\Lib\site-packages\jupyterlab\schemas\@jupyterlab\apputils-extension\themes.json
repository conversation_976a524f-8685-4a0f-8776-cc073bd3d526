{"title": "Theme", "jupyter.lab.setting-icon-label": "Theme Manager", "jupyter.lab.menus": {"main": [{"id": "jp-mainmenu-settings", "items": [{"type": "submenu", "submenu": {"id": "jp-mainmenu-settings-apputilstheme", "label": "Theme", "items": [{"type": "separator"}, {"command": "apputils:adaptive-theme"}, {"type": "separator"}, {"command": "apputils:theme-scrollbars"}, {"type": "separator"}, {"command": "apputils:incr-font-size", "args": {"key": "code-font-size"}}, {"command": "apputils:decr-font-size", "args": {"key": "code-font-size"}}, {"type": "separator"}, {"command": "apputils:incr-font-size", "args": {"key": "content-font-size1"}}, {"command": "apputils:decr-font-size", "args": {"key": "content-font-size1"}}, {"type": "separator"}, {"command": "apputils:incr-font-size", "args": {"key": "ui-font-size1"}}, {"command": "apputils:decr-font-size", "args": {"key": "ui-font-size1"}}]}, "rank": 0}]}]}, "description": "Theme manager settings.", "type": "object", "additionalProperties": false, "definitions": {"cssOverrides": {"type": "object", "additionalProperties": false, "description": "The description field of each item is the CSS property that will be used to validate an override's value", "properties": {"code-font-family": {"type": ["string", "null"], "description": "font-family"}, "code-font-size": {"type": ["string", "null"], "description": "font-size"}, "content-font-family": {"type": ["string", "null"], "description": "font-family"}, "content-font-size1": {"type": ["string", "null"], "description": "font-size"}, "ui-font-family": {"type": ["string", "null"], "description": "font-family"}, "ui-font-size1": {"type": ["string", "null"], "description": "font-size"}}}}, "properties": {"theme": {"type": "string", "title": "Selected Theme", "description": "Application-level visual styling theme. Ignored when Adaptive Theme is enabled.", "default": "JupyterLab Light"}, "adaptive-theme": {"type": "boolean", "title": "Adaptive Theme", "description": "Synchronize visual styling theme with system settings", "default": false}, "preferred-light-theme": {"type": "string", "title": "Preferred Light Theme", "description": "Application-level light visual styling theme. Ignored when Adaptive Theme is disabled.", "default": "JupyterLab Light"}, "preferred-dark-theme": {"type": "string", "title": "Preferred Dark Theme", "description": "Application-level dark visual styling theme. Ignored when Adaptive Theme is disabled.", "default": "<PERSON><PERSON><PERSON><PERSON><PERSON> Dark"}, "theme-scrollbars": {"type": "boolean", "title": "Scrollbar Theming", "description": "Enable/disable styling of the application scrollbars", "default": false}, "overrides": {"title": "Theme CSS Overrides", "description": "Override theme CSS variables by setting key-value pairs here", "$ref": "#/definitions/cssOverrides", "default": {"code-font-family": null, "code-font-size": null, "content-font-family": null, "content-font-size1": null, "ui-font-family": null, "ui-font-size1": null}}}}