{"title": "Docker language server confiuguration", "type": "object", "$comment": "Based on settings of vscode-docker which is distributed under MIT License, Copyright (c) Microsoft Corporation", "properties": {"docker.defaultRegistryPath": {"type": "string", "default": "", "description": "Default registry and path when tagging an image"}, "docker.explorerRefreshInterval": {"type": "number", "default": 2000, "description": "Explorer refresh interval, default is 2000ms"}, "docker.containers.groupBy": {"type": "string", "default": "None", "description": "The property to use when grouping containers.", "enum": ["ContainerId", "ContainerName", "CreatedTime", "FullTag", "ImageId", "Networks", "None", "Ports", "Registry", "Repository", "RepositoryName", "RepositoryNameAndTag", "State", "Status", "Tag"]}, "docker.containers.description": {"type": "array", "default": ["ContainerName", "Status"], "description": "Any secondary properties to display for a container.", "items": {"type": "string", "enum": ["ContainerId", "ContainerName", "CreatedTime", "FullTag", "ImageId", "Networks", "Ports", "Registry", "Repository", "RepositoryName", "RepositoryNameAndTag", "State", "Status", "Tag"]}}, "docker.containers.label": {"type": "string", "default": "FullTag", "description": "The primary property to display for a container.", "enum": ["ContainerId", "ContainerName", "CreatedTime", "FullTag", "ImageId", "Networks", "Ports", "Registry", "Repository", "RepositoryName", "RepositoryNameAndTag", "State", "Status", "Tag"]}, "docker.containers.sortBy": {"type": "string", "default": "CreatedTime", "description": "The property to use when sorting containers.", "enum": ["CreatedTime", "Label"]}, "docker.images.groupBy": {"type": "string", "default": "Repository", "description": "The property to use when grouping images.", "enum": ["CreatedTime", "FullTag", "ImageId", "None", "Registry", "Repository", "RepositoryName", "RepositoryNameAndTag", "Tag"]}, "docker.images.description": {"type": "array", "default": ["CreatedTime"], "description": "Any secondary properties to display for a image.", "items": {"type": "string", "enum": ["CreatedTime", "FullTag", "ImageId", "Registry", "Repository", "RepositoryName", "RepositoryNameAndTag", "Tag"]}}, "docker.images.label": {"type": "string", "default": "Tag", "description": "The primary property to display for a image.", "enum": ["CreatedTime", "FullTag", "ImageId", "Registry", "Repository", "RepositoryName", "RepositoryNameAndTag", "Tag"]}, "docker.images.sortBy": {"type": "string", "default": "CreatedTime", "description": "The property to use when sorting images.", "enum": ["CreatedTime", "Label"]}, "docker.networks.groupBy": {"type": "string", "default": "None", "description": "The property to use when grouping networks.", "enum": ["CreatedTime", "NetworkDriver", "NetworkId", "NetworkName", "None"]}, "docker.networks.description": {"type": "array", "default": ["NetworkDriver", "CreatedTime"], "description": "Any secondary properties to display for a network.", "items": {"type": "string", "enum": ["CreatedTime", "NetworkDriver", "NetworkId", "NetworkName"]}}, "docker.networks.label": {"type": "string", "default": "NetworkName", "description": "The primary property to display for a network.", "enum": ["CreatedTime", "NetworkDriver", "NetworkId", "NetworkName"]}, "docker.networks.sortBy": {"type": "string", "default": "CreatedTime", "description": "The property to use when sorting networks.", "enum": ["CreatedTime", "Label"]}, "docker.volumes.groupBy": {"type": "string", "default": "None", "description": "The property to use when grouping volumes.", "enum": ["CreatedTime", "VolumeName", "None"]}, "docker.volumes.description": {"type": "array", "default": ["CreatedTime"], "description": "Any secondary properties to display for a volume.", "items": {"type": "string", "enum": ["CreatedTime", "VolumeName"]}}, "docker.volumes.label": {"type": "string", "default": "VolumeName", "description": "The primary property to display for a volume.", "enum": ["CreatedTime", "VolumeName"]}, "docker.volumes.sortBy": {"type": "string", "default": "CreatedTime", "description": "The property to use when sorting volumes.", "enum": ["CreatedTime", "Label"]}, "docker.imageBuildContextPath": {"type": "string", "default": "", "description": "Build context PATH to pass to Docker build command"}, "docker.truncateLongRegistryPaths": {"type": "boolean", "default": false, "description": "Truncate long Image and Container registry paths in the Explorer"}, "docker.truncateMaxLength": {"type": "number", "default": 10, "description": "Maximum number of characters for long registry paths in the Explorer, including elipsis"}, "docker.host": {"type": "string", "default": "", "description": "Equivalent to setting the DOCKER_HOST environment variable."}, "docker.certPath": {"type": "string", "default": "", "description": "Equivalent to setting the DOCKER_CERT_PATH environment variable."}, "docker.tlsVerify": {"type": "string", "default": "", "description": "Equivalent to setting the DOCKER_TLS_VERIFY environment variable."}, "docker.machineName": {"type": "string", "default": "", "description": "Equivalent to setting the DOCKER_MACHINE_NAME environment variable."}, "docker.languageserver.diagnostics.deprecatedMaintainer": {"scope": "resource", "type": "string", "default": "warning", "enum": ["ignore", "warning", "error"], "description": "Controls the diagnostic severity for the deprecated MAINTAINER instruction"}, "docker.languageserver.diagnostics.emptyContinuationLine": {"scope": "resource", "type": "string", "default": "warning", "enum": ["ignore", "warning", "error"], "description": "Controls the diagnostic severity for flagging empty continuation lines found in instructions that span multiple lines"}, "docker.languageserver.diagnostics.directiveCasing": {"scope": "resource", "type": "string", "default": "warning", "enum": ["ignore", "warning", "error"], "description": "Controls the diagnostic severity for parser directives that are not written in lowercase"}, "docker.languageserver.diagnostics.instructionCasing": {"scope": "resource", "type": "string", "default": "warning", "enum": ["ignore", "warning", "error"], "description": "Controls the diagnostic severity for instructions that are not written in uppercase"}, "docker.languageserver.diagnostics.instructionCmdMultiple": {"scope": "resource", "type": "string", "default": "warning", "enum": ["ignore", "warning", "error"], "description": "Controls the diagnostic severity for flagging a Dockerfile with multiple CMD instructions"}, "docker.languageserver.diagnostics.instructionEntrypointMultiple": {"scope": "resource", "type": "string", "default": "warning", "enum": ["ignore", "warning", "error"], "description": "Controls the diagnostic severity for flagging a Dockerfile with multiple ENTRYPOINT instructions"}, "docker.languageserver.diagnostics.instructionHealthcheckMultiple": {"scope": "resource", "type": "string", "default": "warning", "enum": ["ignore", "warning", "error"], "description": "Controls the diagnostic severity for flagging a Dockerfile with multiple HEALTHCHECK instructions"}, "docker.languageserver.diagnostics.instructionJSONInSingleQuotes": {"scope": "resource", "type": "string", "default": "warning", "enum": ["ignore", "warning", "error"], "description": "Controls the diagnostic severity for JSON instructions that are written incorrectly with single quotes"}, "docker.languageserver.diagnostics.instructionWorkdirRelative": {"scope": "resource", "type": "string", "default": "warning", "enum": ["ignore", "warning", "error"], "description": "Controls the diagnostic severity for WORKDIR instructions that do not point to an absolute path"}, "docker.attachShellCommand.linuxContainer": {"type": "string", "default": "/bin/sh -c \"[ -e /bin/bash ] && /bin/bash || /bin/sh\"", "description": "Attach command to use for Linux containers"}, "docker.attachShellCommand.windowsContainer": {"type": "string", "default": "powershell", "description": "Attach command to use for Windows containers"}, "docker.dockerComposeBuild": {"type": "boolean", "default": true, "description": "Run docker-compose with the --build argument, defaults to true"}, "docker.dockerComposeDetached": {"type": "boolean", "default": true, "description": "Run docker-compose with the --d (detached) argument, defaults to true"}, "docker.showRemoteWorkspaceWarning": {"type": "boolean", "default": true, "description": "Show a prompt to switch from \"UI\" extension to \"Workspace\" extension if an operation is not supported."}, "docker.dockerPath": {"type": "string", "default": "docker", "description": "Absolute path to Docker client executable ('docker' command). If the path contains whitespace, it needs to be quoted appropriately."}, "docker.enableDockerComposeLanguageService": {"type": "boolean", "default": true, "description": "Whether or not to enable the preview Docker Compose Language Service. Changing requires restart to take effect"}}}