{%- extends 'base.html.j2' -%}
{% from 'mathjax.html.j2' import mathjax %}
{% from 'mermaidjs.html.j2' import mermaid_js %}
{% from 'jupyter_widgets.html.j2' import jupyter_widgets %}

{%- block header -%}
<!DOCTYPE html>
<html lang="{{ resources.language_code }}">
<head>
{%- block html_head -%}
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0">
{% set nb_title = nb.metadata.get('title', resources['metadata']['name']) | escape_html_keep_quotes %}
<title>{{nb_title}}</title>

{%- block html_head_js -%}
{%- block html_head_js_requirejs -%}
<script src="{{ resources.require_js_url }}"></script>
{%- endblock html_head_js_requirejs -%}
{%- endblock html_head_js -%}

{% block jupyter_widgets %}
  {%- if "widgets" in nb.metadata -%}
    {{ jupyter_widgets(resources.jupyter_widgets_base_url, resources.html_manager_semver_range, resources.widget_renderer_url) }}
  {%- endif -%}
{% endblock jupyter_widgets %}

{% block extra_css %}
{% endblock extra_css %}

{% for css in resources.inlining.css -%}
  <style type="text/css">
    {{ css }}
  </style>
{% endfor %}

{% block notebook_css %}
{{ resources.include_css("static/index.css") }}
{% if resources.theme == 'dark' %}
    {{ resources.include_css("static/theme-dark.css") }}
{% elif resources.theme == 'light'  %}
    {{ resources.include_css("static/theme-light.css") }}
{% else %}
    {{ resources.include_lab_theme(resources.theme) }}
{% endif %}
<style type="text/css">
/* Force rendering true colors when outputing to pdf */
* {
  -webkit-print-color-adjust: exact;
}

/* Misc */
a.anchor-link {
  display: none;
}

/* Input area styling */
.jp-InputArea {
  overflow: hidden;
}

.jp-InputArea-editor {
  overflow: hidden;
}

.cm-editor.cm-s-jupyter .highlight pre {
/* weird, but --jp-code-padding defined to be 5px but 4px horizontal padding is hardcoded for pre.cm-line */
  padding: var(--jp-code-padding) 4px;
  margin: 0;

  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  color: inherit;

}

.jp-OutputArea-output pre {
  line-height: inherit;
  font-family: inherit;
}

.jp-RenderedText pre {
  color: var(--jp-content-font-color1);
  font-size: var(--jp-code-font-size);
}

/* Hiding the collapser by default */
.jp-Collapser {
  display: none;
}

@page {
    margin: 0.5in; /* Margin for each printed piece of paper */
}

@media print {
  .jp-Cell-inputWrapper,
  .jp-Cell-outputWrapper {
    display: block;
  }
}
</style>

{% endblock notebook_css %}

{%- block html_head_js_mathjax -%}
{{ mathjax(resources.mathjax_url) }}
{%- endblock html_head_js_mathjax -%}

{%- block html_head_js_mermaidjs -%}
{{ mermaid_js(resources.mermaid_js_url) }}
{%- endblock html_head_js_mermaidjs -%}

{%- block html_head_css -%}
{%- endblock html_head_css -%}

{%- endblock html_head -%}
</head>
{%- endblock header -%}

{%- block body_header -%}
{% if resources.theme == 'dark' %}
<body class="jp-Notebook" data-jp-theme-light="false" data-jp-theme-name="JupyterLab Dark">
{% else %}
<body class="jp-Notebook" data-jp-theme-light="true" data-jp-theme-name="JupyterLab Light">
{% endif %}
<main>
{%- endblock body_header -%}

{% block body_footer %}
</main>
</body>
{% endblock body_footer %}

{% block footer %}
{% block footer_js %}
{% endblock footer_js %}
{{ super() }}
</html>
{% endblock footer %}
