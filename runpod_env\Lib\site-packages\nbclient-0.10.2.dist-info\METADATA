Metadata-Version: 2.4
Name: nbclient
Version: 0.10.2
Summary: A client library for executing notebooks. Formerly nbconvert's ExecutePreprocessor.
Project-URL: Documentation, https://nbclient.readthedocs.io
Project-URL: Funding, https://numfocus.org/
Project-URL: Homepage, https://jupyter.org
Project-URL: Source, https://github.com/jupyter/nbclient
Project-URL: Tracker, https://github.com/jupyter/nbclient/issues
Author-email: Jupyter Development Team <<EMAIL>>
License: BSD 3-Clause License
        
        Copyright (c) 2020-, Jupyter Development Team
        
        All rights reserved.
        
        Redistribution and use in source and binary forms, with or without
        modification, are permitted provided that the following conditions are met:
        
        1. Redistributions of source code must retain the above copyright notice, this
           list of conditions and the following disclaimer.
        
        2. Redistributions in binary form must reproduce the above copyright notice,
           this list of conditions and the following disclaimer in the documentation
           and/or other materials provided with the distribution.
        
        3. Neither the name of the copyright holder nor the names of its
           contributors may be used to endorse or promote products derived from
           this software without specific prior written permission.
        
        THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
        AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
        IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
        DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
        FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
        DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
        SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
        CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
        OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
        OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
License-File: LICENSE
Keywords: executor,jupyter,notebook,pipeline
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Science/Research
Classifier: Intended Audience :: System Administrators
Classifier: License :: OSI Approved :: BSD License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Requires-Python: >=3.9.0
Requires-Dist: jupyter-client>=6.1.12
Requires-Dist: jupyter-core!=5.0.*,>=4.12
Requires-Dist: nbformat>=5.1
Requires-Dist: traitlets>=5.4
Provides-Extra: dev
Requires-Dist: pre-commit; extra == 'dev'
Provides-Extra: docs
Requires-Dist: autodoc-traits; extra == 'docs'
Requires-Dist: flaky; extra == 'docs'
Requires-Dist: ipykernel>=6.19.3; extra == 'docs'
Requires-Dist: ipython; extra == 'docs'
Requires-Dist: ipywidgets; extra == 'docs'
Requires-Dist: mock; extra == 'docs'
Requires-Dist: moto; extra == 'docs'
Requires-Dist: myst-parser; extra == 'docs'
Requires-Dist: nbconvert>=7.1.0; extra == 'docs'
Requires-Dist: pytest-asyncio; extra == 'docs'
Requires-Dist: pytest-cov>=4.0; extra == 'docs'
Requires-Dist: pytest<8,>=7.0; extra == 'docs'
Requires-Dist: sphinx-book-theme; extra == 'docs'
Requires-Dist: sphinx>=1.7; extra == 'docs'
Requires-Dist: sphinxcontrib-spelling; extra == 'docs'
Requires-Dist: testpath; extra == 'docs'
Requires-Dist: xmltodict; extra == 'docs'
Provides-Extra: test
Requires-Dist: flaky; extra == 'test'
Requires-Dist: ipykernel>=6.19.3; extra == 'test'
Requires-Dist: ipython; extra == 'test'
Requires-Dist: ipywidgets; extra == 'test'
Requires-Dist: nbconvert>=7.1.0; extra == 'test'
Requires-Dist: pytest-asyncio; extra == 'test'
Requires-Dist: pytest-cov>=4.0; extra == 'test'
Requires-Dist: pytest<8,>=7.0; extra == 'test'
Requires-Dist: testpath; extra == 'test'
Requires-Dist: xmltodict; extra == 'test'
Description-Content-Type: text/markdown

[![Binder](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/jupyter/nbclient/main?filepath=binder%2Frun_nbclient.ipynb)
[![Build Status](https://github.com/jupyter/nbclient/workflows/CI/badge.svg)](https://github.com/jupyter/nbclient/actions)
[![Documentation Status](https://readthedocs.org/projects/nbclient/badge/?version=latest)](https://nbclient.readthedocs.io/en/latest/?badge=latest)
[![Python 3.7](https://img.shields.io/badge/python-3.7-blue.svg)](https://www.python.org/downloads/release/python-370/)
[![Python 3.8](https://img.shields.io/badge/python-3.8-blue.svg)](https://www.python.org/downloads/release/python-380/)
[![Python 3.9](https://img.shields.io/badge/python-3.9-blue.svg)](https://www.python.org/downloads/release/python-390/)
[![Python 3.10](https://img.shields.io/badge/python-3.10-blue.svg)](https://www.python.org/downloads/release/python-3100/)
[![Python 3.11](https://img.shields.io/badge/python-3.11-blue.svg)](https://www.python.org/downloads/release/python-3110/)
[![Code style: black](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/ambv/black)

# nbclient

**NBClient** lets you **execute** notebooks.

A client library for programmatic notebook execution, **NBClient** is a tool for running Jupyter Notebooks in
different execution contexts, including the command line.

## Interactive Demo

To demo **NBClient** interactively, click this Binder badge to start the demo:

[![Binder](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/jupyter/nbclient/main?filepath=binder%2Frun_nbclient.ipynb)

## Installation

In a terminal, run:

```
python3 -m pip install nbclient
```

## Documentation

See [ReadTheDocs](https://nbclient.readthedocs.io/en/latest/) for more in-depth details about the project and the
[API Reference](https://nbclient.readthedocs.io/en/latest/reference/index.html).

## Python Version Support

This library currently supports Python 3.6+ versions. As minor Python
versions are officially sunset by the Python org, nbclient will similarly
drop support in the future.

## Origins

This library used to be part of the [nbconvert](https://nbconvert.readthedocs.io/en/latest/) project. NBClient extracted nbconvert's `ExecutePreprocessor`into its own library for easier updating and importing by downstream libraries and applications.

## Relationship to JupyterClient

NBClient and JupyterClient are distinct projects.

`jupyter_client` is a client library for the jupyter protocol. Specifically, `jupyter_client` provides the Python API
for starting, managing and communicating with Jupyter kernels.

While, nbclient allows notebooks to be run in different execution contexts.

## About the Jupyter Development Team

The Jupyter Development Team is the set of all contributors to the Jupyter project.
This includes all of the Jupyter subprojects.

The core team that coordinates development on GitHub can be found here:
https://github.com/jupyter/.

## Our Copyright Policy

Jupyter uses a shared copyright model. Each contributor maintains copyright
over their contributions to Jupyter. But, it is important to note that these
contributions are typically only changes to the repositories. Thus, the Jupyter
source code, in its entirety is not the copyright of any single person or
institution.  Instead, it is the collective copyright of the entire Jupyter
Development Team.  If individual contributors want to maintain a record of what
changes/contributions they have specific copyright on, they should indicate
their copyright in the commit message of the change, when they commit the
change to one of the Jupyter repositories.

With this in mind, the following banner should be used in any source code file
to indicate the copyright and license terms:

```
# Copyright (c) Jupyter Development Team.
# Distributed under the terms of the Modified BSD License.
```
