#!/usr/bin/env python3
"""
Debug RunPod API key permissions and test different endpoints.
"""

import requests
import json

def test_api_key_permissions():
    """Test API key against different RunPod endpoints."""
    api_key = "rpa_RB6ABSXXW9HPJAB8ZB3YOKT6IP53X7EG6F2VVN3M1slbla"
    
    print("🔍 RunPod API Key Permission Debugger")
    print("=" * 50)
    print(f"Testing API Key: {api_key[:15]}...")
    print()
    
    # Test different API endpoints
    tests = [
        {
            "name": "GraphQL API (Pods)",
            "url": "https://api.runpod.io/graphql",
            "method": "POST",
            "headers": {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            },
            "data": {
                "query": "query { myself { id email } }"
            }
        },
        {
            "name": "GraphQL API (GPU Types)",
            "url": "https://api.runpod.io/graphql", 
            "method": "POST",
            "headers": {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            },
            "data": {
                "query": "query { gpuTypes { id displayName } }"
            }
        },
        {
            "name": "GraphQL API (Pods List)",
            "url": "https://api.runpod.io/graphql",
            "method": "POST", 
            "headers": {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            },
            "data": {
                "query": "query { myself { pods { id name } } }"
            }
        }
    ]
    
    results = []
    
    for test in tests:
        print(f"🧪 Testing: {test['name']}")
        
        try:
            if test['method'] == 'POST':
                response = requests.post(
                    test['url'],
                    headers=test['headers'],
                    json=test['data'],
                    timeout=30
                )
            else:
                response = requests.get(
                    test['url'],
                    headers=test['headers'],
                    timeout=30
                )
            
            print(f"   Status Code: {response.status_code}")
            
            if response.status_code == 200:
                print("   ✅ SUCCESS!")
                try:
                    data = response.json()
                    if 'data' in data:
                        print(f"   📄 Response: {json.dumps(data, indent=2)[:200]}...")
                    elif 'errors' in data:
                        print(f"   ⚠️  GraphQL Errors: {data['errors']}")
                    results.append((test['name'], True, "Success"))
                except:
                    print(f"   📄 Response: {response.text[:200]}...")
                    results.append((test['name'], True, "Success (non-JSON)"))
            
            elif response.status_code == 401:
                print("   ❌ UNAUTHORIZED (401)")
                print("   → API key is invalid or lacks permissions")
                results.append((test['name'], False, "Unauthorized"))
                
            elif response.status_code == 403:
                print("   ❌ FORBIDDEN (403)")
                print("   → API key lacks specific permissions for this endpoint")
                results.append((test['name'], False, "Forbidden"))
                
            else:
                print(f"   ❌ ERROR: {response.status_code}")
                print(f"   Response: {response.text[:200]}...")
                results.append((test['name'], False, f"HTTP {response.status_code}"))
                
        except requests.exceptions.Timeout:
            print("   ⏰ TIMEOUT")
            results.append((test['name'], False, "Timeout"))
            
        except requests.exceptions.RequestException as e:
            print(f"   ❌ REQUEST ERROR: {e}")
            results.append((test['name'], False, f"Request Error: {e}"))
            
        except Exception as e:
            print(f"   ❌ UNEXPECTED ERROR: {e}")
            results.append((test['name'], False, f"Error: {e}"))
        
        print()
    
    # Summary
    print("=" * 50)
    print("📋 SUMMARY")
    print("=" * 50)
    
    success_count = sum(1 for _, success, _ in results if success)
    total_count = len(results)
    
    print(f"Tests passed: {success_count}/{total_count}")
    print()
    
    for test_name, success, message in results:
        status = "✅" if success else "❌"
        print(f"{status} {test_name}: {message}")
    
    print()
    
    if success_count == 0:
        print("🔧 TROUBLESHOOTING STEPS:")
        print("1. Check RunPod Console → Settings → API Keys")
        print("2. Ensure your API key has 'All' or 'Read/Write' permissions")
        print("3. If it shows 'Restricted', click edit and change to 'All'")
        print("4. Wait 2-3 minutes after making changes")
        print("5. Verify your account has sufficient credits")
        print("6. Try generating a completely new API key")
        
    elif success_count < total_count:
        print("⚠️  PARTIAL SUCCESS:")
        print("Some endpoints work, others don't. This suggests permission issues.")
        print("Check your API key permissions in RunPod console.")
        
    else:
        print("🎉 ALL TESTS PASSED!")
        print("Your API key is working correctly!")
        print("The MCP server should work now.")
    
    return success_count > 0

def check_api_key_format():
    """Check if the API key format looks correct."""
    api_key = "rpa_RB6ABSXXW9HPJAB8ZB3YOKT6IP53X7EG6F2VVN3M1slbla"
    
    print("🔍 API Key Format Check")
    print("=" * 30)
    print(f"Key: {api_key}")
    print(f"Length: {len(api_key)} characters")
    print(f"Starts with 'rpa_': {'✅' if api_key.startswith('rpa_') else '❌'}")
    print(f"Contains only valid chars: {'✅' if api_key.replace('_', '').isalnum() else '❌'}")
    print()
    
    if not api_key.startswith('rpa_'):
        print("⚠️  API key should start with 'rpa_'")
        return False
    
    if len(api_key) < 30:
        print("⚠️  API key seems too short")
        return False
    
    print("✅ API key format looks correct")
    return True

def main():
    """Main debugging function."""
    print("🐛 RunPod API Key Debugger")
    print("=" * 40)
    
    # Check format first
    format_ok = check_api_key_format()
    print()
    
    if format_ok:
        # Test permissions
        permissions_ok = test_api_key_permissions()
        
        if permissions_ok:
            print("\n🎯 NEXT STEPS:")
            print("Your API key is working! You can now:")
            print("1. Use the RunPod MCP server")
            print("2. Run: python runpod_mcp_deploy.py")
            print("3. Start training your model!")
        else:
            print("\n🔧 RECOMMENDED ACTIONS:")
            print("1. Go to https://console.runpod.io/user/settings")
            print("2. Check your API key permissions")
            print("3. Change from 'Restricted' to 'All' if needed")
            print("4. Wait a few minutes and try again")
    else:
        print("❌ API key format issue detected")
        print("Please check your API key in RunPod console")

if __name__ == "__main__":
    main()
