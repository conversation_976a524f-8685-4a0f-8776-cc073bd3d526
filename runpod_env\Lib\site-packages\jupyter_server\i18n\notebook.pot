# Translations template for Jupy<PERSON>.
# Copyright (C) 2017 ORGANIZATION
# This file is distributed under the same license as the Jupyter project.
# <AUTHOR> <EMAIL>, 2017.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Jupyter VERSION\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2017-07-08 21:52-0500\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.3.4\n"

#: jupyter_server/serverapp.py:53
msgid "The Jupyter Server requires tornado >= 4.0"
msgstr ""

#: jupyter_server/serverapp.py:57
msgid "The Jupyter Server requires tornado >= 4.0, but you have < 1.1.0"
msgstr ""

#: jupyter_server/serverapp.py:59
#, python-format
msgid "The Jupyter Server requires tornado >= 4.0, but you have %s"
msgstr ""

#: jupyter_server/serverapp.py:389
msgid "List currently running Jupyter servers."
msgstr ""

#: jupyter_server/serverapp.py:393
msgid "Produce machine-readable JSON output."
msgstr ""

#: jupyter_server/serverapp.py:397
msgid "If True, each line of output will be a JSON object with the details from the server info file."
msgstr ""

#: jupyter_server/serverapp.py:402
msgid "Currently running servers:"
msgstr ""

#: jupyter_server/serverapp.py:419
msgid "Don't open the jupyter_server in a browser after startup."
msgstr ""

#: jupyter_server/serverapp.py:423
msgid "DISABLED: use %pylab or %matplotlib in the notebook to enable matplotlib."
msgstr ""

#: jupyter_server/serverapp.py:439
msgid "Allow the server to be run from root user."
msgstr ""

#: jupyter_server/serverapp.py:470
msgid ""
"The Jupyter Server.\n"
"    \n"
"    This launches a Tornado-based Jupyter Server."
msgstr ""

#: jupyter_server/serverapp.py:540
msgid "Set the Access-Control-Allow-Credentials: true header"
msgstr ""

#: jupyter_server/serverapp.py:544
msgid "Whether to allow the user to run the Jupyter server as root."
msgstr ""

#: jupyter_server/serverapp.py:548
msgid "The default URL to redirect to from `/`"
msgstr ""

#: jupyter_server/serverapp.py:552
msgid "The IP address the Jupyter server will listen on."
msgstr ""

#: jupyter_server/serverapp.py:565
#, python-format
msgid ""
"Cannot bind to localhost, using 127.0.0.1 as default ip\n"
"%s"
msgstr ""

#: jupyter_server/serverapp.py:579
msgid "The port the Jupyter server will listen on."
msgstr ""

#: jupyter_server/serverapp.py:583
msgid "The number of additional ports to try if the specified port is not available."
msgstr ""

#: jupyter_server/serverapp.py:587
msgid "The full path to an SSL/TLS certificate file."
msgstr ""

#: jupyter_server/serverapp.py:591
msgid "The full path to a private key file for usage with SSL/TLS."
msgstr ""

#: jupyter_server/serverapp.py:595
msgid "The full path to a certificate authority certificate for SSL/TLS client authentication."
msgstr ""

#: jupyter_server/serverapp.py:599
msgid "The file where the cookie secret is stored."
msgstr ""

#: jupyter_server/serverapp.py:628
#, python-format
msgid "Writing Jupyter server cookie secret to %s"
msgstr ""

#: jupyter_server/serverapp.py:635
#, python-format
msgid "Could not set permissions on %s"
msgstr ""

#: jupyter_server/serverapp.py:640
msgid ""
"Token used for authenticating first-time connections to the server.\n"
"\n"
"        When no password is enabled,\n"
"        the default is to generate a new, random token.\n"
"\n"
"        Setting to an empty string disables authentication altogether, which is NOT RECOMMENDED.\n"
"        "
msgstr ""

#: jupyter_server/serverapp.py:650
msgid ""
"One-time token used for opening a browser.\n"
"        Once used, this token cannot be used again.\n"
"        "
msgstr ""

#: jupyter_server/serverapp.py:726
msgid ""
"Specify Where to open the server on startup. This is the\n"
"        `new` argument passed to the standard library method `webbrowser.open`.\n"
"        The behaviour is not guaranteed, but depends on browser support. Valid\n"
"        values are:\n"
"            2 opens a new tab,\n"
"            1 opens a new window,\n"
"            0 opens in an existing window.\n"
"        See the `webbrowser.open` documentation for details.\n"
"        "
msgstr ""

#: jupyter_server/serverapp.py:742
msgid ""
"\n"
"    webapp_settings is deprecated, use tornado_settings.\n"
msgstr ""

#: jupyter_server/serverapp.py:746
msgid "Supply overrides for the tornado.web.Application that the Jupyter server uses."
msgstr ""

#: jupyter_server/serverapp.py:750
msgid ""
"\n"
"        Set the tornado compression options for websocket connections.\n"
"\n"
"        This value will be returned from :meth:`WebSocketHandler.get_compression_options`.\n"
"        None (default) will disable compression.\n"
"        A dict (even an empty one) will enable compression.\n"
"\n"
"        See the tornado docs for WebSocketHandler.get_compression_options for details.\n"
"        "
msgstr ""

#: jupyter_server/serverapp.py:761
msgid "Supply overrides for terminado. Currently only supports \"shell_command\"."
msgstr ""

#: jupyter_server/serverapp.py:764
msgid "Extra keyword arguments to pass to `set_secure_cookie`. See tornado's set_secure_cookie docs for details."
msgstr ""

#: jupyter_server/serverapp.py:768
msgid ""
"Supply SSL options for the tornado HTTPServer.\n"
"            See the tornado docs for details."
msgstr ""

#: jupyter_server/serverapp.py:772
msgid "Supply extra arguments that will be passed to Jinja environment."
msgstr ""

#: jupyter_server/serverapp.py:776
msgid "Extra variables to supply to jinja templates when rendering."
msgstr ""

#: jupyter_server/serverapp.py:816
msgid "base_project_url is deprecated, use base_url"
msgstr ""

#: jupyter_server/serverapp.py:832
msgid "Path to search for custom.js, css"
msgstr ""

#: jupyter_server/serverapp.py:844
msgid ""
"Extra paths to search for serving jinja templates.\n"
"\n"
"        Can be used to override templates from jupyter_server.templates."
msgstr ""

#: jupyter_server/serverapp.py:900
#, python-format
msgid "Using MathJax: %s"
msgstr ""

#: jupyter_server/serverapp.py:903
msgid "The MathJax.js configuration file that is to be used."
msgstr ""

#: jupyter_server/serverapp.py:908
#, python-format
msgid "Using MathJax configuration file: %s"
msgstr ""

#: jupyter_server/serverapp.py:920
msgid "The kernel manager class to use."
msgstr ""

#: jupyter_server/serverapp.py:926
msgid "The session manager class to use."
msgstr ""

#: jupyter_server/serverapp.py:932
msgid "The config manager class to use"
msgstr ""

#: jupyter_server/serverapp.py:953
msgid "The login handler class to use."
msgstr ""

#: jupyter_server/serverapp.py:960
msgid "The logout handler class to use."
msgstr ""

#: jupyter_server/serverapp.py:964
msgid "Whether to trust or not X-Scheme/X-Forwarded-Proto and X-Real-Ip/X-Forwarded-For headerssent by the upstream reverse proxy. Necessary if the proxy handles SSL"
msgstr ""

#: jupyter_server/serverapp.py:976
msgid ""
"\n"
"        DISABLED: use %pylab or %matplotlib in the notebook to enable matplotlib.\n"
"        "
msgstr ""

#: jupyter_server/serverapp.py:988
msgid "Support for specifying --pylab on the command line has been removed."
msgstr ""

#: jupyter_server/serverapp.py:990
msgid "Please use `%pylab{0}` or `%matplotlib{0}` in the notebook itself."
msgstr ""

#: jupyter_server/serverapp.py:995
msgid "The directory to use for notebooks and kernels."
msgstr ""

#: jupyter_server/serverapp.py:1018
#, python-format
msgid "No such notebook dir: '%r'"
msgstr ""

#: jupyter_server/serverapp.py:1036
msgid "server_extensions is deprecated, use jpserver_extensions"
msgstr ""

#: jupyter_server/serverapp.py:1040
msgid "Dict of Python modules to load as notebook server extensions. Entry values can be used to enable and disable the loading of the extensions. The extensions will be loaded in alphabetical order."
msgstr ""

#: jupyter_server/serverapp.py:1049
msgid "Reraise exceptions encountered loading server extensions?"
msgstr ""

#: jupyter_server/serverapp.py:1052
msgid ""
"(msgs/sec)\n"
"        Maximum rate at which messages can be sent on iopub before they are\n"
"        limited."
msgstr ""

#: jupyter_server/serverapp.py:1056
msgid ""
"(bytes/sec)\n"
"        Maximum rate at which stream output can be sent on iopub before they are\n"
"        limited."
msgstr ""

#: jupyter_server/serverapp.py:1060
msgid ""
"(sec) Time window used to \n"
"        check the message and data rate limits."
msgstr ""

#: jupyter_server/serverapp.py:1071
#, python-format
msgid "No such file or directory: %s"
msgstr ""

#: jupyter_server/serverapp.py:1141
msgid "Notebook servers are configured to only be run with a password."
msgstr ""

#: jupyter_server/serverapp.py:1142
msgid "Hint: run the following command to set a password"
msgstr ""

#: jupyter_server/serverapp.py:1143
msgid "\t$ python -m jupyter_server.auth password"
msgstr ""

#: jupyter_server/serverapp.py:1181
#, python-format
msgid "The port %i is already in use, trying another port."
msgstr ""

#: jupyter_server/serverapp.py:1184
#, python-format
msgid "Permission to listen on port %i denied"
msgstr ""

#: jupyter_server/serverapp.py:1193
msgid "ERROR: the Jupyter server could not be started because no available port could be found."
msgstr ""

#: jupyter_server/serverapp.py:1199
msgid "[all ip addresses on your system]"
msgstr ""

#: jupyter_server/serverapp.py:1223
#, python-format
msgid "Terminals not available (error was %s)"
msgstr ""

#: jupyter_server/serverapp.py:1259
msgid "interrupted"
msgstr ""

#: jupyter_server/serverapp.py:1261
msgid "y"
msgstr ""

#: jupyter_server/serverapp.py:1262
msgid "n"
msgstr ""

#: jupyter_server/serverapp.py:1263
#, python-format
msgid "Shutdown this notebook server (%s/[%s])? "
msgstr ""

#: jupyter_server/serverapp.py:1269
msgid "Shutdown confirmed"
msgstr ""

#: jupyter_server/serverapp.py:1273
msgid "No answer for 5s:"
msgstr ""

#: jupyter_server/serverapp.py:1274
msgid "resuming operation..."
msgstr ""

#: jupyter_server/serverapp.py:1282
#, python-format
msgid "received signal %s, stopping"
msgstr ""

#: jupyter_server/serverapp.py:1338
#, python-format
msgid "Error loading server extension %s"
msgstr ""

#: jupyter_server/serverapp.py:1369
#, python-format
msgid "Shutting down %d kernels"
msgstr ""

#: jupyter_server/serverapp.py:1375
#, python-format
msgid "%d active kernel"
msgid_plural "%d active kernels"
msgstr[0] ""
msgstr[1] ""

#: jupyter_server/serverapp.py:1379
#, python-format
msgid ""
"The Jupyter Notebook is running at:\n"
"\r"
"%s"
msgstr ""

#: jupyter_server/serverapp.py:1426
msgid "Running as root is not recommended. Use --allow-root to bypass."
msgstr ""

#: jupyter_server/serverapp.py:1432
msgid "Use Control-C to stop this server and shut down all kernels (twice to skip confirmation)."
msgstr ""

#: jupyter_server/serverapp.py:1434
msgid "Welcome to Project Jupyter! Explore the various tools available and their corresponding documentation. If you are interested in contributing to the platform, please visit the communityresources section at http://jupyter.org/community.html."
msgstr ""

#: jupyter_server/serverapp.py:1445
#, python-format
msgid "No web browser found: %s."
msgstr ""

#: jupyter_server/serverapp.py:1450
#, python-format
msgid "%s does not exist"
msgstr ""

#: jupyter_server/serverapp.py:1484
msgid "Interrupted..."
msgstr ""

#: jupyter_server/services/contents/filemanager.py:506
#, python-format
msgid "Serving notebooks from local directory: %s"
msgstr ""

#: jupyter_server/services/contents/manager.py:68
msgid "Untitled"
msgstr ""
