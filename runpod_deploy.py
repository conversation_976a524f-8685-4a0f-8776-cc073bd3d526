#!/usr/bin/env python3
"""
RunPod CLI deployment script for Qwen3 fine-tuning.
This script helps you deploy and manage your training job using RunPod CLI.
"""

import subprocess
import json
import time
import os
from pathlib import Path

class RunPodCLIManager:
    """Manage RunPod operations using the CLI."""
    
    def __init__(self):
        self.cli_path = "./runpodctl.exe"
        
    def run_command(self, cmd, timeout=60):
        """Run a RunPod CLI command."""
        try:
            full_cmd = [self.cli_path] + cmd
            print(f"🔧 Running: {' '.join(full_cmd)}")
            
            result = subprocess.run(
                full_cmd,
                capture_output=True,
                text=True,
                timeout=timeout
            )
            
            if result.returncode == 0:
                return True, result.stdout
            else:
                return False, result.stderr
                
        except subprocess.TimeoutExpired:
            return False, "Command timed out"
        except Exception as e:
            return False, str(e)
    
    def list_gpu_types(self):
        """List available GPU types."""
        print("🔍 Checking available GPU types...")
        success, output = self.run_command(["get", "gpu"])
        
        if success:
            print("✅ Available GPU types:")
            print(output)
            return True
        else:
            print(f"❌ Failed to get GPU types: {output}")
            return False
    
    def create_training_pod(self, pod_name="qwen3-training", gpu_type="NVIDIA RTX 4090", image="runpod/pytorch:2.1.0-py3.10-cuda11.8.0-devel-ubuntu22.04"):
        """Create a pod for training."""
        print(f"🚀 Creating training pod: {pod_name}")
        
        cmd = [
            "create", "pod",
            "--name", pod_name,
            "--image", image,
            "--gpu-type", gpu_type,
            "--volume-size", "50",  # 50GB storage
            "--container-disk-size", "20",  # 20GB container disk
            "--ports", "8888/http",  # Jupyter port
        ]
        
        success, output = self.run_command(cmd, timeout=120)
        
        if success:
            print("✅ Pod created successfully!")
            print(output)
            
            # Extract pod ID from output (you might need to adjust this parsing)
            lines = output.strip().split('\n')
            for line in lines:
                if 'Pod ID:' in line or len(line.split()) > 0:
                    # Try to extract pod ID - this might need adjustment based on actual output
                    pass
            
            return True, output
        else:
            print(f"❌ Failed to create pod: {output}")
            return False, output
    
    def list_pods(self):
        """List all pods."""
        print("📋 Listing your pods...")
        success, output = self.run_command(["get", "pod"])
        
        if success:
            print("✅ Your pods:")
            print(output)
            return True, output
        else:
            print(f"❌ Failed to list pods: {output}")
            return False, output
    
    def get_pod_info(self, pod_id):
        """Get detailed information about a specific pod."""
        print(f"🔍 Getting info for pod: {pod_id}")
        success, output = self.run_command(["get", "pod", pod_id])
        
        if success:
            print(f"✅ Pod {pod_id} info:")
            print(output)
            return True, output
        else:
            print(f"❌ Failed to get pod info: {output}")
            return False, output
    
    def upload_training_files(self, pod_id):
        """Upload training files to the pod."""
        print(f"📤 Uploading training files to pod {pod_id}...")
        
        # Files to upload
        files_to_upload = [
            "finetune_qwen3.py",
            "requirements.txt",  # We'll create this
        ]
        
        for file_path in files_to_upload:
            if os.path.exists(file_path):
                print(f"📁 Uploading {file_path}...")
                success, output = self.run_command([
                    "send", pod_id, file_path, f"/workspace/{file_path}"
                ])
                
                if success:
                    print(f"✅ Uploaded {file_path}")
                else:
                    print(f"❌ Failed to upload {file_path}: {output}")
            else:
                print(f"⚠️  File not found: {file_path}")
    
    def start_training(self, pod_id):
        """Start the training process on the pod."""
        print(f"🏋️ Starting training on pod {pod_id}...")
        
        # Commands to run on the pod
        training_commands = [
            "cd /workspace",
            "pip install -r requirements.txt",
            "python finetune_qwen3.py"
        ]
        
        for cmd in training_commands:
            print(f"🔧 Executing: {cmd}")
            success, output = self.run_command([
                "exec", pod_id, "--", "bash", "-c", cmd
            ])
            
            if success:
                print(f"✅ Command completed: {cmd}")
                print(output)
            else:
                print(f"❌ Command failed: {cmd}")
                print(f"Error: {output}")
                return False
        
        return True
    
    def monitor_training(self, pod_id):
        """Monitor training progress."""
        print(f"👀 Monitoring training on pod {pod_id}...")
        print("Use these commands to monitor:")
        print(f"  {self.cli_path} logs {pod_id}")
        print(f"  {self.cli_path} exec {pod_id} -- tail -f /workspace/qwen3-python-finetune/trainer_state.json")
        print(f"  {self.cli_path} exec {pod_id} -- nvidia-smi")
    
    def download_results(self, pod_id):
        """Download training results."""
        print(f"📥 Downloading results from pod {pod_id}...")
        
        # Create local results directory
        results_dir = Path("./training_results")
        results_dir.mkdir(exist_ok=True)
        
        # Files to download
        files_to_download = [
            "/workspace/qwen3-python-final/",
            "/workspace/qwen3-python-finetune/",
        ]
        
        for remote_path in files_to_download:
            local_path = results_dir / Path(remote_path).name
            print(f"📁 Downloading {remote_path} to {local_path}...")
            
            success, output = self.run_command([
                "receive", pod_id, remote_path, str(local_path)
            ])
            
            if success:
                print(f"✅ Downloaded {remote_path}")
            else:
                print(f"❌ Failed to download {remote_path}: {output}")
    
    def cleanup_pod(self, pod_id):
        """Stop and delete the pod."""
        print(f"🧹 Cleaning up pod {pod_id}...")
        
        # Stop the pod first
        success, output = self.run_command(["stop", "pod", pod_id])
        if success:
            print("✅ Pod stopped")
        else:
            print(f"⚠️  Failed to stop pod: {output}")
        
        # Delete the pod
        success, output = self.run_command(["delete", "pod", pod_id])
        if success:
            print("✅ Pod deleted")
        else:
            print(f"❌ Failed to delete pod: {output}")

def create_requirements_file():
    """Create requirements.txt for the training environment."""
    requirements = """
torch>=2.1.0
transformers>=4.35.0
datasets>=2.14.0
peft>=0.6.0
accelerate>=0.24.0
bitsandbytes>=0.41.0
flash-attn>=2.3.0
numpy>=1.24.0
scipy>=1.11.0
scikit-learn>=1.3.0
"""
    
    with open("requirements.txt", "w") as f:
        f.write(requirements.strip())
    
    print("✅ Created requirements.txt")

def main():
    """Main deployment workflow."""
    print("🚀 RunPod CLI Training Deployment")
    print("=" * 50)
    
    # Create requirements file
    create_requirements_file()
    
    # Initialize CLI manager
    cli = RunPodCLIManager()
    
    # Check available GPUs
    cli.list_gpu_types()
    
    print("\n" + "=" * 50)
    print("📋 DEPLOYMENT OPTIONS:")
    print("1. Create training pod")
    print("2. List existing pods")
    print("3. Upload files to existing pod")
    print("4. Start training on existing pod")
    print("5. Monitor training")
    print("6. Download results")
    print("7. Cleanup pod")
    print("=" * 50)
    
    choice = input("\nEnter your choice (1-7): ").strip()
    
    if choice == "1":
        pod_name = input("Pod name (default: qwen3-training): ").strip() or "qwen3-training"
        gpu_type = input("GPU type (default: NVIDIA RTX 4090): ").strip() or "NVIDIA RTX 4090"
        cli.create_training_pod(pod_name, gpu_type)
        
    elif choice == "2":
        cli.list_pods()
        
    elif choice == "3":
        pod_id = input("Enter pod ID: ").strip()
        if pod_id:
            cli.upload_training_files(pod_id)
        
    elif choice == "4":
        pod_id = input("Enter pod ID: ").strip()
        if pod_id:
            cli.start_training(pod_id)
        
    elif choice == "5":
        pod_id = input("Enter pod ID: ").strip()
        if pod_id:
            cli.monitor_training(pod_id)
        
    elif choice == "6":
        pod_id = input("Enter pod ID: ").strip()
        if pod_id:
            cli.download_results(pod_id)
        
    elif choice == "7":
        pod_id = input("Enter pod ID: ").strip()
        if pod_id:
            confirm = input(f"Are you sure you want to delete pod {pod_id}? (y/N): ").strip().lower()
            if confirm == 'y':
                cli.cleanup_pod(pod_id)
    
    else:
        print("❌ Invalid choice")

if __name__ == "__main__":
    main()
