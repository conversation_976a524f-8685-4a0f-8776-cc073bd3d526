#!/usr/bin/env python3
"""
Test RunPod MCP server authentication and basic functionality.
"""

def test_mcp_authentication():
    """Test if RunPod MCP server is working."""
    print("🔍 Testing RunPod MCP Server Authentication")
    print("=" * 50)
    
    tests = [
        ("List Pods", lambda: list_pods_RunPod()),
        ("List Templates", lambda: list_templates_RunPod()),
        ("List Network Volumes", lambda: list_network_volumes_RunPod()),
    ]
    
    for test_name, test_func in tests:
        print(f"\n🧪 Testing: {test_name}")
        try:
            result = test_func()
            print(f"✅ {test_name} - Success!")
            print(f"   Result type: {type(result)}")
            if isinstance(result, (dict, list)):
                print(f"   Data structure: {len(result) if isinstance(result, list) else 'dict'}")
            return True
        except Exception as e:
            print(f"❌ {test_name} - Failed: {e}")
            if "401" in str(e):
                print("   → This is an authentication error")
                print("   → Check your API key in RunPod console")
                return False
            elif "403" in str(e):
                print("   → This is a permissions error")
                print("   → Your API key might need additional permissions")
                return False
    
    return False

def show_mcp_capabilities():
    """Show what the MCP server can do."""
    print("\n🚀 RunPod MCP Server Capabilities")
    print("=" * 50)
    
    capabilities = {
        "Pod Management": [
            "list-pods_RunPod - List all your pods",
            "get-pod_RunPod - Get detailed pod information", 
            "create-pod_RunPod - Create new pods",
            "update-pod_RunPod - Update pod configuration",
            "start-pod_RunPod - Start stopped pods",
            "stop-pod_RunPod - Stop running pods", 
            "delete-pod_RunPod - Delete pods"
        ],
        "Serverless Endpoints": [
            "list-endpoints_RunPod - List all endpoints",
            "get-endpoint_RunPod - Get endpoint details",
            "create-endpoint_RunPod - Create new endpoints",
            "update-endpoint_RunPod - Update endpoint settings",
            "delete-endpoint_RunPod - Delete endpoints"
        ],
        "Templates": [
            "list-templates_RunPod - List available templates",
            "get-template_RunPod - Get template details",
            "create-template_RunPod - Create custom templates",
            "update-template_RunPod - Update templates",
            "delete-template_RunPod - Delete templates"
        ],
        "Storage": [
            "list-network-volumes_RunPod - List network volumes",
            "get-network-volume_RunPod - Get volume details",
            "create-network-volume_RunPod - Create new volumes",
            "update-network-volume_RunPod - Resize volumes",
            "delete-network-volume_RunPod - Delete volumes"
        ],
        "Registry Auth": [
            "list-container-registry-auths_RunPod - List registry auths",
            "get-container-registry-auth_RunPod - Get auth details",
            "create-container-registry-auth_RunPod - Add registry auth",
            "delete-container-registry-auth_RunPod - Remove registry auth"
        ]
    }
    
    for category, tools in capabilities.items():
        print(f"\n📂 {category}:")
        for tool in tools:
            print(f"   • {tool}")
    
    print(f"\n✨ Total: {sum(len(tools) for tools in capabilities.values())} powerful tools!")

def main():
    """Main test function."""
    print("🧪 RunPod MCP Server Test")
    print("=" * 30)
    
    # Show capabilities
    show_mcp_capabilities()
    
    # Test authentication
    auth_works = test_mcp_authentication()
    
    print("\n" + "=" * 50)
    print("📋 SUMMARY")
    print("=" * 50)
    
    if auth_works:
        print("🎉 RunPod MCP Server is working!")
        print("✅ You can now use the MCP server for training deployment")
        print("🚀 Run: python runpod_mcp_deploy.py")
    else:
        print("❌ Authentication failed")
        print("🔧 Next steps:")
        print("   1. Check your API key in RunPod console")
        print("   2. Ensure the key has proper permissions")
        print("   3. Try generating a new API key")
        print("   4. Verify your RunPod account is active")
    
    return auth_works

if __name__ == "__main__":
    main()
