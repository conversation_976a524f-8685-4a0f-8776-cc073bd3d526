#!/usr/bin/env python3
"""
Quick training deployment script using RunPod CLI.
This automates the entire process from pod creation to training.
"""

import subprocess
import time
import sys

def run_cli_command(cmd, description=""):
    """Run a RunPod CLI command and handle output."""
    if description:
        print(f"🔧 {description}")
    
    try:
        print(f"   Running: ./runpodctl.exe {' '.join(cmd)}")
        result = subprocess.run(
            ["./runpodctl.exe"] + cmd,
            capture_output=True,
            text=True,
            timeout=120
        )
        
        if result.returncode == 0:
            print(f"✅ Success!")
            if result.stdout.strip():
                print(f"   Output: {result.stdout.strip()}")
            return True, result.stdout
        else:
            print(f"❌ Failed: {result.stderr}")
            return False, result.stderr
            
    except subprocess.TimeoutExpired:
        print("❌ Command timed out")
        return False, "Timeout"
    except Exception as e:
        print(f"❌ Error: {e}")
        return False, str(e)

def main():
    """Quick training deployment."""
    print("🚀 Quick Qwen3 Training Deployment")
    print("=" * 50)
    
    # Step 1: Check available GPUs
    print("\n📋 Step 1: Checking available GPUs...")
    success, output = run_cli_command(["get", "gpu"], "Getting GPU types")
    
    if not success:
        print("❌ Cannot proceed without GPU information")
        return
    
    # Step 2: Create training pod
    print("\n🚀 Step 2: Creating training pod...")
    
    pod_config = [
        "create", "pod",
        "--name", "qwen3-training",
        "--image", "runpod/pytorch:2.1.0-py3.10-cuda11.8.0-devel-ubuntu22.04",
        "--gpu-type", "NVIDIA RTX 4090",  # You can change this
        "--volume-size", "50",
        "--container-disk-size", "20",
        "--ports", "8888/http"
    ]
    
    success, output = run_cli_command(pod_config, "Creating pod")
    
    if not success:
        print("❌ Failed to create pod. Trying with different GPU...")
        # Try with RTX 3090
        pod_config[7] = "NVIDIA RTX 3090"
        success, output = run_cli_command(pod_config, "Creating pod with RTX 3090")
        
        if not success:
            print("❌ Cannot create pod. Please check your account and try manually.")
            return
    
    # Extract pod ID (this is a simplified extraction - you might need to adjust)
    print("\n📋 Pod created! Please note the pod ID from the output above.")
    pod_id = input("Enter the pod ID: ").strip()
    
    if not pod_id:
        print("❌ Pod ID required to continue")
        return
    
    # Step 3: Wait for pod to be ready
    print(f"\n⏳ Step 3: Waiting for pod {pod_id} to be ready...")
    print("   This may take a few minutes...")
    
    for i in range(10):  # Wait up to 10 minutes
        time.sleep(60)  # Wait 1 minute
        success, output = run_cli_command(["get", "pod", pod_id], f"Checking pod status (attempt {i+1})")
        
        if success and "running" in output.lower():
            print("✅ Pod is ready!")
            break
        else:
            print(f"   Pod not ready yet... waiting (attempt {i+1}/10)")
    else:
        print("❌ Pod took too long to start. Please check manually.")
        return
    
    # Step 4: Upload training files
    print(f"\n📤 Step 4: Uploading training files to pod {pod_id}...")
    
    files_to_upload = [
        ("finetune_qwen3.py", "/workspace/finetune_qwen3.py"),
        ("requirements.txt", "/workspace/requirements.txt")
    ]
    
    for local_file, remote_path in files_to_upload:
        success, output = run_cli_command(
            ["send", pod_id, local_file, remote_path],
            f"Uploading {local_file}"
        )
        
        if not success:
            print(f"❌ Failed to upload {local_file}")
            return
    
    # Step 5: Install dependencies and start training
    print(f"\n🏋️ Step 5: Installing dependencies and starting training...")
    
    training_commands = [
        "cd /workspace && pip install -r requirements.txt",
        "cd /workspace && python finetune_qwen3.py"
    ]
    
    for cmd in training_commands:
        print(f"   Executing: {cmd}")
        success, output = run_cli_command(
            ["exec", pod_id, "--", "bash", "-c", cmd],
            f"Running: {cmd}"
        )
        
        if not success and "pip install" in cmd:
            print("⚠️  Dependency installation failed, but continuing...")
        elif not success:
            print(f"❌ Training command failed: {cmd}")
            return
    
    # Step 6: Provide monitoring instructions
    print(f"\n👀 Step 6: Training started! Here's how to monitor:")
    print("=" * 50)
    print(f"📊 Check logs:           ./runpodctl.exe logs {pod_id}")
    print(f"📈 Check GPU usage:      ./runpodctl.exe exec {pod_id} -- nvidia-smi")
    print(f"📁 Check training dir:   ./runpodctl.exe exec {pod_id} -- ls -la /workspace/")
    print(f"🔍 Monitor progress:     ./runpodctl.exe exec {pod_id} -- tail -f /workspace/qwen3-python-finetune/trainer_state.json")
    print(f"🖥️  Connect via SSH:      ./runpodctl.exe ssh {pod_id}")
    
    print(f"\n📥 When training is complete, download results:")
    print(f"   ./runpodctl.exe receive {pod_id} /workspace/qwen3-python-final ./results/")
    
    print(f"\n🧹 When done, cleanup:")
    print(f"   ./runpodctl.exe stop pod {pod_id}")
    print(f"   ./runpodctl.exe delete pod {pod_id}")
    
    print("\n✅ Deployment complete! Your model is training on RunPod.")

if __name__ == "__main__":
    main()
