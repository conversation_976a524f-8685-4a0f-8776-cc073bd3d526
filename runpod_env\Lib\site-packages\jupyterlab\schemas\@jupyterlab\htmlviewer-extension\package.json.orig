{"name": "@jupyterlab/htmlviewer-extension", "version": "4.4.4", "description": "JupyterLab extension to render HTML files", "keywords": ["jup<PERSON><PERSON>", "jupyterlab"], "homepage": "https://github.com/jupyterlab/jupyterlab", "bugs": {"url": "https://github.com/jupyterlab/jupyterlab"}, "repository": {"type": "git", "url": "https://github.com/jupyterlab/jupyterlab.git"}, "license": "BSD-3-<PERSON><PERSON>", "author": "Project Jupyter Contributors", "sideEffects": ["style/**/*"], "main": "lib/index.js", "types": "lib/index.d.ts", "style": "style/index.css", "files": ["lib/**/*.{d.ts,eot,gif,html,jpg,js,js.map,json,png,svg,woff2,ttf}", "schema/*.json", "style/**/*.{css,eot,gif,html,jpg,json,png,svg,woff2,ttf}", "style/index.js", "src/**/*.{ts,tsx}"], "scripts": {"build": "tsc -b", "clean": "rimraf lib && rimraf tsconfig.tsbuildinfo", "watch": "tsc -w"}, "dependencies": {"@jupyterlab/application": "^4.4.4", "@jupyterlab/apputils": "^4.5.4", "@jupyterlab/docregistry": "^4.4.4", "@jupyterlab/htmlviewer": "^4.4.4", "@jupyterlab/observables": "^5.4.4", "@jupyterlab/settingregistry": "^4.4.4", "@jupyterlab/translation": "^4.4.4", "@jupyterlab/ui-components": "^4.4.4"}, "devDependencies": {"rimraf": "~5.0.5", "typescript": "~5.5.4"}, "publishConfig": {"access": "public"}, "jupyterlab": {"extension": true, "schemaDir": "schema"}, "styleModule": "style/index.js"}