#!/usr/bin/env python3
import torch
from transformers import AutoToken<PERSON>, AutoModelForCausalLM, TrainingArguments, Trainer, DataCollatorForLanguageModeling
from peft import LoraConfig, get_peft_model, TaskType, prepare_model_for_kbit_training
from torch.utils.data import Dataset

class SimpleDataset(Dataset):
    def __init__(self, data, tokenizer, max_length=512):
        self.data = data
        self.tokenizer = tokenizer
        self.max_length = max_length
        
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        item = self.data[idx]
        text = f"Instruction: {item['instruction']}\nOutput: {item['output']}"
        
        encoding = self.tokenizer(
            text,
            truncation=True,
            max_length=self.max_length,
            padding="max_length",
            return_tensors="pt"
        )
        
        return {
            "input_ids": encoding["input_ids"].flatten(),
            "attention_mask": encoding["attention_mask"].flatten(),
            "labels": encoding["input_ids"].flatten()
        }

print("🚀 Loading local Qwen3-8B model...")

# Load from local directory
model_path = "./qwen3-8b-model"
tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True, local_files_only=True)
if tokenizer.pad_token is None:
    tokenizer.pad_token = tokenizer.eos_token

# Load model in float32 first, then convert
model = AutoModelForCausalLM.from_pretrained(
    model_path,
    torch_dtype=torch.float32,  # Load in float32 first
    device_map="auto",
    trust_remote_code=True,
    local_files_only=True
)

print("✅ Model loaded successfully!")

# Prepare model for training - CRITICAL for PEFT
model = prepare_model_for_kbit_training(model)

# Setup LoRA with more conservative settings
lora_config = LoraConfig(
    task_type=TaskType.CAUSAL_LM,
    r=8,  # Reduced rank
    lora_alpha=16,  # Reduced alpha
    lora_dropout=0.05,
    target_modules=["q_proj", "k_proj", "v_proj", "o_proj"],
    bias="none",
    inference_mode=False,  # Explicitly set for training
)

model = get_peft_model(model, lora_config)

# Enable training mode and ensure gradients
model.train()
for param in model.parameters():
    if param.requires_grad:
        param.data = param.data.float()  # Ensure float32 for gradients

print(f"📊 Trainable parameters: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")

# Verify we have trainable parameters
trainable_params = [p for p in model.parameters() if p.requires_grad]
print(f"🔍 Number of trainable parameter tensors: {len(trainable_params)}")

# Create Python coding data
print("📚 Creating Python coding dataset...")
data = [
    {"instruction": "Write a function to add two numbers", "output": "def add(a, b):\n    return a + b"},
    {"instruction": "Create a function to multiply two numbers", "output": "def multiply(a, b):\n    return a * b"},
    {"instruction": "Write a function to find the maximum of two numbers", "output": "def max_num(a, b):\n    return max(a, b)"},
    {"instruction": "Create a function to check if a number is even", "output": "def is_even(n):\n    return n % 2 == 0"},
]

print(f"📝 Using {len(data)} examples for training")

# Create dataset
dataset = SimpleDataset(data, tokenizer, max_length=256)  # Shorter sequences

# Training arguments - Conservative settings
training_args = TrainingArguments(
    output_dir="./qwen3-finetuned",
    num_train_epochs=1,
    per_device_train_batch_size=1,
    gradient_accumulation_steps=1,  # Reduced
    learning_rate=1e-4,  # Lower learning rate
    fp16=False,
    bf16=False,  # Disable mixed precision completely
    logging_steps=1,
    save_steps=10,
    remove_unused_columns=False,
    dataloader_pin_memory=False,
    gradient_checkpointing=False,  # Disable for now
    max_grad_norm=1.0,
    dataloader_num_workers=0,
    warmup_steps=0,
    weight_decay=0.01,
)

# Data collator
data_collator = DataCollatorForLanguageModeling(
    tokenizer=tokenizer,
    mlm=False,
)

# Create trainer
trainer = Trainer(
    model=model,
    args=training_args,
    train_dataset=dataset,
    data_collator=data_collator,
)

print("🎯 Starting training...")
try:
    trainer.train()
    print("✅ Training completed!")
    trainer.save_model("./qwen3-finetuned-final")
    print("💾 Model saved to ./qwen3-finetuned-final")
    
    # Test the model
    print("🧪 Testing the trained model...")
    model.eval()
    test_input = "Instruction: Write a function to subtract two numbers\nOutput:"
    inputs = tokenizer(test_input, return_tensors="pt").to(model.device)
    
    with torch.no_grad():
        outputs = model.generate(
            **inputs,
            max_new_tokens=50,
            do_sample=True,
            temperature=0.7,
            pad_token_id=tokenizer.eos_token_id
        )
    
    response = tokenizer.decode(outputs[0], skip_special_tokens=True)
    print(f"🤖 Model response:\n{response}")
    
except Exception as e:
    print(f"❌ Training failed: {e}")
    import traceback
    traceback.print_exc()

print("🎉 Script completed!")
