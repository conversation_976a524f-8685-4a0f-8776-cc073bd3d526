#!/usr/bin/env python3
"""
Test script for RunPod SDK installation and basic functionality.
This script tests the SDK installation and basic API connectivity.
Note: Windows compatibility test - avoiding serverless modules that require fcntl.
"""

import os
import sys

def test_runpod_import():
    """Test importing specific RunPod modules that work on Windows."""
    print("Testing RunPod import compatibility on Windows...")

    try:
        # Try importing just the core API functionality
        import runpod.api
        print("✅ runpod.api imported successfully")

        # Try importing version info
        import runpod.version
        print("✅ runpod.version imported successfully")

        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_sdk_version():
    """Test that we can get version info from RunPod SDK."""
    print("=" * 50)
    print("Testing RunPod SDK Version")
    print("=" * 50)

    try:
        import runpod.version
        version = runpod.version.get_version()
        print(f"✅ RunPod SDK version: {version}")
        return True
    except Exception as e:
        print(f"❌ Error getting RunPod SDK version: {e}")
        return False

def test_api_key_setup():
    """Test API key configuration."""
    print("\n" + "=" * 50)
    print("Testing API Key Configuration")
    print("=" * 50)

    try:
        import runpod.api

        # Set the API key
        api_key = "rpa_RB6ABSXXW9HPJAB8ZB3YOKT6IP53X7EG6F2VVN3M1slbla"
        runpod.api.api_key = api_key

        # Verify it's set
        if runpod.api.api_key:
            print("✅ API key has been set successfully")
            print(f"✅ API key starts with: {runpod.api.api_key[:10]}...")
            return True
        else:
            print("❌ Failed to set API key")
            return False
    except Exception as e:
        print(f"❌ Error setting up API key: {e}")
        return False

def test_basic_api_connection():
    """Test basic API connectivity (if possible without making actual calls)."""
    print("\n" + "=" * 50)
    print("Testing Basic API Setup")
    print("=" * 50)
    
    try:
        # Just test that we can access the API modules without errors
        print("✅ RunPod API modules accessible")
        
        # Check if we have the main API components
        if hasattr(runpod, 'api_key'):
            print("✅ API key attribute available")
        
        # Note: We're not making actual API calls here to avoid charges
        # or hitting rate limits during testing
        print("ℹ️  API connection test completed (no actual API calls made)")
        return True
        
    except Exception as e:
        print(f"❌ Error testing API setup: {e}")
        return False

def main():
    """Main test function."""
    print("RunPod SDK Installation and Configuration Test (Windows)")
    print("=" * 60)

    tests_passed = 0
    total_tests = 4

    # Test 1: Basic Import Test
    if test_runpod_import():
        tests_passed += 1

    # Test 2: SDK Version
    if test_sdk_version():
        tests_passed += 1

    # Test 3: API Key Setup
    if test_api_key_setup():
        tests_passed += 1

    # Test 4: Basic API Setup
    if test_basic_api_connection():
        tests_passed += 1
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"Tests passed: {tests_passed}/{total_tests}")

    if tests_passed == total_tests:
        print("🎉 All tests passed! RunPod SDK is ready to use.")
        print("\nNext steps:")
        print("- You can now use the RunPod SDK API functions in your Python scripts")
        print("- Check the documentation for API usage examples")
        print("- Consider setting RUNPOD_API_KEY as an environment variable")
        print("- Note: Serverless functions may have Windows compatibility issues")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        print("Note: This is a Windows compatibility test - some features may be limited.")
    
    return tests_passed == total_tests

if __name__ == "__main__":
    main()
