{%- extends 'lab/base.html.j2' -%}
{% from 'cellslidedata.j2' import cellslidedata %}

{%- block any_cell scoped -%}
{%- if cell.metadata.get('slide_start', False) -%}
<section {{ cellslidedata(cell) }}>
{%- endif -%}
{%- if cell.metadata.get('subslide_start', False) -%}
<section {{ cellslidedata(cell) }}>
{%- endif -%}
{%- if cell.metadata.get('fragment_start', False) -%}
<div class="fragment" {{ cellslidedata(cell) }}>
{%- endif -%}

{%- if cell.metadata.slide_type == 'notes' -%}
<aside class="notes">
{{ super() }}
</aside>
{%- elif cell.metadata.slide_type == 'skip' -%}
{%- else -%}
{{ super() }}
{%- endif -%}

{%- if cell.metadata.get('fragment_end', False) -%}
</div>
{%- endif -%}
{%- if cell.metadata.get('subslide_end', False) -%}
</section>
{%- endif -%}
{%- if cell.metadata.get('slide_end', False) -%}
</section>
{%- endif -%}
{%- endblock any_cell -%}
