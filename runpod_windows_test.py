#!/usr/bin/env python3
"""
Windows-specific RunPod SDK test.
This script attempts to work around Windows compatibility issues.
"""

import sys
import os

def test_direct_api_import():
    """Try to import RunPod API components directly."""
    print("=" * 60)
    print("Testing Direct RunPod API Import (Windows Workaround)")
    print("=" * 60)
    
    try:
        # Try to import specific modules that might work on Windows
        sys.path.insert(0, 'runpod_env/Lib/site-packages')
        
        # Try importing just the API client parts
        from runpod.api.ctl import get_pods, create_pod
        print("✅ Successfully imported runpod.api.ctl functions")
        
        from runpod.api.endpoints import get_endpoints
        print("✅ Successfully imported runpod.api.endpoints functions")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Trying alternative approach...")
        
        try:
            # Try a more basic approach
            import runpod.api
            print("✅ Basic runpod.api import successful")
            return True
        except Exception as e2:
            print(f"❌ Alternative import also failed: {e2}")
            return False
    
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_manual_api_setup():
    """Test setting up API manually without full SDK."""
    print("\n" + "=" * 60)
    print("Testing Manual API Setup")
    print("=" * 60)
    
    try:
        # Try to set up basic API configuration manually
        api_key = "rpa_RB6ABSXXW9HPJAB8ZB3YOKT6IP53X7EG6F2VVN3M1slbla"
        
        # Test basic HTTP request setup (without actually making calls)
        import requests
        
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        print("✅ API headers configured successfully")
        print(f"✅ API key configured: {api_key[:10]}...")
        print("✅ Requests library available for API calls")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in manual API setup: {e}")
        return False

def test_alternative_installation():
    """Check if we can use pip to install a Windows-compatible version."""
    print("\n" + "=" * 60)
    print("Checking Installation Details")
    print("=" * 60)
    
    try:
        import subprocess
        
        # Check what we actually have installed
        result = subprocess.run(['pip', 'show', 'runpod'], 
                              capture_output=True, text=True, cwd='runpod_env/Scripts')
        
        if result.returncode == 0:
            print("✅ RunPod package information:")
            print(result.stdout)
        else:
            print("❌ Could not get package information")
            
        return True
        
    except Exception as e:
        print(f"❌ Error checking installation: {e}")
        return False

def show_workaround_info():
    """Show information about Windows compatibility and workarounds."""
    print("\n" + "=" * 60)
    print("WINDOWS COMPATIBILITY INFORMATION")
    print("=" * 60)
    
    print("🔍 ISSUE IDENTIFIED:")
    print("   The RunPod SDK has a dependency on 'fcntl' module which is Unix-only.")
    print("   This prevents the full SDK from working on Windows.")
    
    print("\n💡 POSSIBLE WORKAROUNDS:")
    print("   1. Use WSL (Windows Subsystem for Linux) to run the SDK")
    print("   2. Use Docker with a Linux container")
    print("   3. Make direct HTTP API calls using requests library")
    print("   4. Use the RunPod CLI tool (runpodctl.exe) instead")
    
    print("\n🛠️  RECOMMENDED APPROACH FOR WINDOWS:")
    print("   Since you have runpodctl.exe in your directory, use that for")
    print("   RunPod operations, or make direct API calls with requests.")
    
    print(f"\n🔑 YOUR API KEY: rpa_RB6ABSXXW9HPJAB8ZB3YOKT6IP53X7EG6F2VVN3M1slbla")
    print("   (First 10 chars: rpa_RB6ABS...)")

def main():
    """Main test function."""
    print("RunPod SDK Windows Compatibility Test")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 3
    
    # Test 1: Direct API Import
    if test_direct_api_import():
        tests_passed += 1
    
    # Test 2: Manual API Setup
    if test_manual_api_setup():
        tests_passed += 1
    
    # Test 3: Installation Check
    if test_alternative_installation():
        tests_passed += 1
    
    # Show results and workaround info
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"Tests passed: {tests_passed}/{total_tests}")
    
    # Always show workaround info since Windows compatibility is limited
    show_workaround_info()
    
    return tests_passed >= 1  # Consider success if at least one test passes

if __name__ == "__main__":
    main()
