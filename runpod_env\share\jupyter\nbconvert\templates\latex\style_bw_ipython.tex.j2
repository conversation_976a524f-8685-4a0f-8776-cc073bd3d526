((= Black&white ipython input/output style =))

((*- extends 'base.tex.j2' -*))

%===============================================================================
% Input
%===============================================================================

((* block input scoped *))
((*- if resources.global_content_filter.include_input_prompt *))
((( add_prompt(cell.source, cell, 'In ') )))
((* else *))
(((cell.source)))
((* endif -*))
((* endblock input *))


%===============================================================================
% Output
%===============================================================================

((* block execute_result scoped *))
    ((*- for type in output.data | filter_data_type -*))
        ((*- if resources.global_content_filter.include_output_prompt -*))
            ((*- if type in ['text/plain'] *))
((( add_prompt(output.data['text/plain'], cell, 'Out') )))
            ((*- else -*))
\verb+Out[((( cell.execution_count )))]:+((( super() )))
            ((*- endif -*))
        ((*- else -*))
            ((*- if type in ['text/plain'] *))
((( output.data['text/plain'] )))
            ((*- else -*))
\verb+((( super() )))
            ((*- endif -*))
        ((*- endif -*))
    ((*- endfor -*))
((* endblock execute_result *))


%==============================================================================
% Support Macros
%==============================================================================

% Name: draw_prompt
% Purpose: Renders an output/input prompt
((* macro add_prompt(text, cell, prompt) -*))
    ((*- if cell.execution_count is defined -*))
    ((*- set execution_count = "" ~ (cell.execution_count | replace(None, " ")) -*))
    ((*- else -*))
    ((*- set execution_count = " " -*))
    ((*- endif -*))
    ((*- set indentation = " " * (execution_count | length + 7) -*))
\begin{verbatim}
(((- text | add_prompts(first=prompt ~ '[' ~ execution_count ~ ']: ', cont=indentation) -)))
\end{verbatim}
((*- endmacro *))
