{"title": "Theme", "description": "Theme manager settings.", "properties": {"theme": {"type": "string", "title": "Selected Theme", "default": "JupyterLab Light"}, "codeCellConfig": {"title": "Code Cell Configuration", "description": "The configuration for all code cells.", "$ref": "#/definitions/editorConfig", "default": {"autoClosingBrackets": true, "cursorBlinkRate": 530, "fontFamily": null, "fontSize": null, "lineHeight": null, "lineNumbers": false, "lineWrap": "off", "matchBrackets": true, "readOnly": false, "insertSpaces": true, "tabSize": 4, "wordWrapColumn": 80, "rulers": [], "codeFolding": false, "lineWiseCopyCut": true}}}, "type": "object"}