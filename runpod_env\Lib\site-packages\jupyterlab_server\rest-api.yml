# see me at: https://petstore.swagger.io/?url=https://raw.githubusercontent.com/jupyterlab/jupyterlab_server/main/jupyterlab_server/rest-api.yml#/default
openapi: "3.0.3"
info:
  title: JupyterLab Server
  description: The REST API for JupyterLab Server
  version: 1.0.0
  license:
    name: BSD-3-Clause

paths:
  /lab/api/listings/%40jupyterlab/extensionmanager-extension/listings.json:
    get:
      summary: Get Extension Listings Specs
      description: |
        Gets the list of extension metadata for the application
      responses:
        "200":
          description: The Extension Listing specs
          content:
            application/json:
              schema:
                properties:
                  blocked_extension_uris:
                    type: array
                    description: list of blocked extension uris
                    items:
                      type: string
                  allowed_extension_uris:
                    type: array
                    description: list of allowed extension uris
                    items:
                      type: string
                  blocked_extensions:
                    type: array
                    description: list of blocked extensions
                    items:
                      $ref: "#/components/schemas/ListEntry"
                  allowed_extensions:
                    type: array
                    description: list of blocked extensions
                    items:
                      $ref: "#/components/schemas/ListEntry"

  /lab/api/settings/:
    get:
      summary: Get Settings List
      description: |
        Gets the list of all application settings data
      responses:
        "200":
          description: The Application Settings Data
          content:
            application/json:
              schema:
                properties:
                  settings:
                    type: array
                    description: List of application settings entries
                    items:
                      $ref: "#/components/schemas/SettingsEntry"

  /lab/api/settings/{schema_name}:
    parameters:
      - name: schema_name
        description: Schema Name
        in: path
        required: true
        schema:
          type: string
    get:
      summary: Get the settings data for a given schema
      description: |
        Gets the settings data for a given schema
      responses:
        "200":
          description: The Settings Data
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SettingsEntry"
    put:
      summary: Override the settings data for a given schema
      description: |
        Overrides the settings data for a given schema
      requestBody:
        required: true
        description: raw settings data
        content:
          application/json:
            schema:
              type: object
              properties:
                raw:
                  type: string
      responses:
        "204":
          description: The setting has been updated

  /lab/api/themes/{theme_file}:
    parameters:
      - name: theme_file
        description: Theme file path
        in: path
        required: true
        schema:
          type: string
    get:
      summary: Get a static theme file
      description: |
        Gets the static theme file at a given path
      responses:
        "200":
          description: The Theme File

  /lab/api/translations/:
    get:
      summary: Get Translation Bundles
      description: |
        Gets the list of translation bundles
      responses:
        "200":
          description: The Extension Listing specs
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    additionalProperties:
                      $ref: "#/components/schemas/TranslationEntry"
                  message:
                    type: string

  /lab/api/translations/{locale}:
    parameters:
      - name: locale
        description: Locale name
        in: path
        required: true
        schema:
          type: string
    get:
      summary: Get the translation data for locale
      description: |
        Gets the translation data for a given locale
      responses:
        "200":
          description: The Local Data
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                  message:
                    type: string

  /lab/api/workspaces/:
    get:
      summary: Get Workspace Data
      description: |
        Gets the list of workspace data
      responses:
        "200":
          description: The Workspace specs
          content:
            application/json:
              schema:
                type: object
                properties:
                  workspaces:
                    type: object
                    properties:
                      ids:
                        type: array
                        items:
                          type: string
                      values:
                        type: array
                        items:
                          $ref: "#/components/schemas/Workspace"

  /lab/api/workspaces/{space_name}:
    parameters:
      - name: space_name
        description: Workspace name
        in: path
        required: true
        schema:
          type: string
    get:
      summary: Get the workspace data for name
      description: |
        Gets the workspace data for a given workspace name
      responses:
        "200":
          description: The Workspace Data
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Workspace"
    put:
      summary: Override the workspace data for a given name
      description: |
        Overrides the workspace data for a given workspace name
      requestBody:
        required: true
        description: raw workspace data
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Workspace"
      responses:
        "204":
          description: The workspace has been updated

    delete:
      summary: Delete the workspace data for a given name
      description: |
        Deletes the workspace data for a given workspace name
      responses:
        "204":
          description: The workspace has been deleted
  /lab/api/licenses/:
    get:
      summary: License report
      description: |
        Get the third-party licenses for the core application and all federated
        extensions
      parameters:
        - name: full_text
          description: Return full license texts
          in: query
          schema:
            type: boolean
        - name: format
          in: query
          description: The format in which to report licenses
          schema:
            type: string
            enum:
              - csv
              - json
              - markdown
        - name: bundles
          description: A regular expression to limit the names of bundles reported
          in: query
          schema:
            type: string
        - name: download
          in: query
          description: Whether to set a representative filename header
          schema:
            type: boolean
      responses:
        "200":
          description: A license report
          content:
            application/markdown:
              schema:
                type: string
            text/csv:
              schema:
                type: string
            application/json:
              schema:
                $ref: "#/components/schemas/LicenseBundles"

components:
  schemas:
    ListEntry:
      type: object
      properties:
        name:
          type: string
        regexp:
          type: string
        type:
          type: string
        reason:
          type: string
        creation_date:
          type: string
        last_update_date:
          type: string
    SettingsEntry:
      type: object
      properties:
        id:
          type: string
        schema:
          type: object
        version:
          type: string
        raw:
          type: string
        settings:
          type: object
        warning:
          type: string
          nullable: true
        last_modified:
          type: string
          nullable: true
        created:
          type: string
          nullable: true
    TranslationEntry:
      type: object
      properties:
        data:
          type: object
          properties:
            displayName:
              type: string
            nativeName:
              type: string
        message:
          type: string
    Workspace:
      type: object
      properties:
        data:
          type: object
        metadata:
          type: object
          properties:
            id:
              type: string
            last_modified:
              type: string
            created:
              type: string
    LicenseBundles:
      type: object
      properties:
        bundles:
          type: object
          additionalProperties:
            type: object
            properties:
              packages:
                type: array
                items:
                  type: object
                  properties:
                    extractedText:
                      type: string
                    licenseId:
                      type: string
                    name:
                      type: string
                    versionInfo:
                      type: string
